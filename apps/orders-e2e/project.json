{"name": "orders-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/orders-e2e/src", "projectType": "application", "tags": ["scope:orders"], "implicitDependencies": ["orders"], "targets": {"e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/orders-e2e/cypress.config.ts", "devServerTarget": "orders:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "orders:serve:production"}}}, "lint": {"executor": "@nx/eslint:lint"}}}