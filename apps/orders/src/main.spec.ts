import { enableProdMode } from '@angular/core';
import { environment } from './environments/environment';

jest.mock('@angular/core', () => ({
  enableProdMode: jest.fn(),
}));

jest.mock('@angular/platform-browser-dynamic', () => ({
  platformBrowserDynamic: jest.fn(() => ({
    bootstrapModule: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('./app/app.module', () => ({
  AppModule: jest.fn(),
}));

jest.mock('./environments/environment', () => ({
  environment: { production: false },
}));

describe('Bootstrap Code', () => {
  let originalReadyState: string;

  beforeEach(() => {
    originalReadyState = document.readyState;
    Object.defineProperty(document, 'readyState', {
      writable: true,
      value: 'loading',
    });
    jest.clearAllMocks();
  });

  afterEach(() => {
    Object.defineProperty(document, 'readyState', {
      writable: true,
      value: originalReadyState,
    });
  });

  it('should call enableProdMode when in production environment', () => {
    environment.production = true;
    require('./main');

    expect(enableProdMode).toHaveBeenCalled();
  });

  it('should not call enableProdMode when not in production environment', () => {
    environment.production = false;
    require('./main');

    expect(enableProdMode).not.toHaveBeenCalled();
  });
});
