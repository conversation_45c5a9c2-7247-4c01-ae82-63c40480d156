import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

export const PPS_PRIME_PRESET = definePreset(Aura, {
  semantic: {
    primary: {
      50: 'var(--color-primary-01)',
      100: 'var(--color-primary-04)',
      200: 'var(--color-primary-04)',
      300: 'var(--color-primary-10)',
      400: 'var(--color-primary-20)',
      500: 'var(--color-primary-50)',
      600: 'var(--color-primary-50)',
      700: 'var(--color-primary-50)',
      800: 'var(--color-primary-50)',
      900: 'var(--color-primary-50)',
      950: 'var(--color-primary-50)',
    },
    colorScheme: {
      light: {
        root: {
          datatableHeaderCell: {
            background: '{surface.100} !important',
          },
        },
        surface: {
          0: 'var(--color-white)',
          50: 'var(--color-gray-0)',
          100: 'var(--color-gray-0)',
          200: 'var(--color-gray-05)',
          300: 'var(--color-gray-05)',
          400: 'var(--color-gray-40)',
          500: 'var(--color-gray-60)',
          600: 'var(--color-gray-80)',
          700: 'var(--color-gray-90)',
          800: 'var(--color-gray-100)',
          900: 'var(--color-gray-140)',
          950: 'var(--color-black)',
        },
        formField: {
          hoverBorderColor: '{primary.color}',
          borderRadius: 'var(--border-radius-xxxs)',
          borderColor: '{primary.300}',
          selectDropdownColor: '{surface.800}',
          placeholderColor: '{surface.500}',
        },
      },
      dark: {
        formField: {
          hoverBorderColor: '{primary.color}',
          borderRadius: 'var(--border-radius-xxxs)',
          borderColor: '{primary.300}',
        },
        datatableHeaderCell: {
          background: '{surface.300}',
        },
      },
    },
  },
});
