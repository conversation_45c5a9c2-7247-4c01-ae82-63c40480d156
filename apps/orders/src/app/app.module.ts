import {
  AgentP<PERSON>ageEvents,
  AgentPackageEventsModule,
  AssignedIssueChangedEventHandler as AssignedIssueChangedEventHandlerAgentPackageLib,
} from '@agent-package';
import { AiEvents, RequestAiSmartAssistantResponseEventHandler } from '@ai';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { AuthInterceptorModule, AuthStateModule } from '@core/auth';
import {
  ConfigStateModule,
  HttpConfigServiceModule,
  InMemoryConfigStorageModule,
  LOADS_CONFIG_COMMAND_PORT,
  LoadsConfigCommandPort,
} from '@core/config';
import {
  FeatureFlagsStateModule,
  GraphqlFeatureFlagsServiceModule,
  InMemoryFeatureFlagsStorageModule,
} from '@core/feature-flags';
import { GoogleService, GoogleServiceModule } from '@core/google';
import { GraphQLClientModule } from '@core/graphql-client';
import { CoreAngularModule } from '@ecs/angular';
import { ApplicationEventBusModule } from '@esky-pps-nx/event-bus';
import { ConsoleLoggerServiceModule, LOGGING_CONFIG, LoggingLevel } from '@esky-pps-nx/logger';
import {
  AssignedIssueChangedEventHandler as AssignedIssueChangedEventHandlerGenInfoLib,
  GeneralPackageDetailsEvents,
  GeneralPackageInfoEventsModule,
  LoadingSmartAssistantActionsEventHandler,
} from '@general-package-details';
import {
  ShowImportBookingDialogRequestEventHandler,
  ShowImportOfferDialogRequestedEventHandler,
} from '@import-booking';
import { IssuesEvents, PackageAvailableLoadedEventHandler, ShowCreateDialogRequestEventHandler } from '@issues';
import { ShowEmailPreviewRequestEventHandler, ShowEmailPreviewRequestEventHandlerModule } from '@notifications';
import { PackageAvailabilityEvents } from '@package-availability';
import { PintaModule } from '@pinta/angular';
import { LOCAL_STORAGE_PORT, UiColorSpinnerComponentModule } from '@shared/ui';
import { AbstractSecurityStorage, AuthModule, DefaultLocalStorageService, LogLevel } from 'angular-auth-oidc-client';
import { CookieModule } from 'ngx-cookie';
import { MARKED_OPTIONS, provideMarkdown } from 'ngx-markdown';
import { providePrimeNG } from 'primeng/config';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { PPS_PRIME_PRESET } from './config/prime-ng-preset';
import { ActivitiesPageModule } from './pages/activities/activities.page-module';
import { CallbackPageModule } from './pages/callback/callback.page-module';
import { EditBookingPageModule } from './pages/edit-booking/edit-booking.page-module';
import { ImportBookingPageModule } from './pages/import-booking/import-booking.page-module';
import { LabelsPageModule } from './pages/labels/labels.page-module';
import { NotificationsPageModule } from './pages/notifications/notifications.page-module';
import { PackageDetailsPageModule } from './pages/package-details/package-details.page-module';
import { PackagesPageModule } from './pages/packages/packages.page-module';
import { UnauthorizedPageModule } from './pages/unauthorized/unauthorized.page-module';
import { UserPageModule } from './pages/user/user.page-module';
import { UsersPageModule } from './pages/users/users.page-module';

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    ApplicationEventBusModule.forRoot([
      [ShowCreateDialogRequestEventHandler, [GeneralPackageDetailsEvents.ShowCreateIssueDialogRequest]],
      [ShowImportBookingDialogRequestEventHandler, [GeneralPackageDetailsEvents.ShowImportBookingDialogRequest]],
      [ShowImportOfferDialogRequestedEventHandler, [GeneralPackageDetailsEvents.ShowImportOfferDialogRequested]],
      [RequestAiSmartAssistantResponseEventHandler, [GeneralPackageDetailsEvents.RequestAiSmartAssistantResponse]],
      [AssignedIssueChangedEventHandlerGenInfoLib, [IssuesEvents.AssignedIssueChanged]],
      [AssignedIssueChangedEventHandlerAgentPackageLib, [IssuesEvents.AssignedIssueChanged]],
      [PackageAvailableLoadedEventHandler, [PackageAvailabilityEvents.PackageAvailabilityLoaded]],
      [ShowEmailPreviewRequestEventHandler, [AgentPackageEvents.ShowEmailPreviewDialogRequest]],
      [LoadingSmartAssistantActionsEventHandler, [AiEvents.LoadingSmartAssistantActions]],
    ]),
    AppRoutingModule,
    AuthInterceptorModule,
    AuthModule.forRoot({
      config: {
        authority: typeof window !== 'undefined' && 'authUrl' in window ? window['authUrl' as keyof typeof window] : '',
        redirectUrl: `${typeof window !== 'undefined' ? window.location.origin : ''}/callback`,
        clientId: 'pps',
        responseType: 'code',
        scope: 'openid offline_access package profile',
        silentRenew: true,
        useRefreshToken: true,
        logLevel: LogLevel.None,
        historyCleanupOff: false,
        autoUserInfo: false,
      },
    }),
    AuthStateModule,
    ActivitiesPageModule,
    AgentPackageEventsModule,
    BrowserAnimationsModule,
    BrowserModule.withServerTransition({ appId: 'serverApp' }),
    CallbackPageModule,
    CookieModule.withOptions(),
    ConfigStateModule,
    ConsoleLoggerServiceModule,
    CoreAngularModule,
    EditBookingPageModule,
    FeatureFlagsStateModule,
    GeneralPackageInfoEventsModule,
    GoogleServiceModule,
    GraphQLClientModule.forRoot('/v2/agent/graphql'),
    GraphqlFeatureFlagsServiceModule,
    HttpConfigServiceModule,
    ImportBookingPageModule,
    InMemoryConfigStorageModule,
    InMemoryFeatureFlagsStorageModule,
    LabelsPageModule,
    NotificationsPageModule,
    PackagesPageModule,
    PackageDetailsPageModule,
    PintaModule,
    ShowEmailPreviewRequestEventHandlerModule,
    UiColorSpinnerComponentModule,
    UnauthorizedPageModule,
    UserPageModule,
    UsersPageModule,
  ],
  providers: [
    provideMarkdown({
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true,
          pedantic: false,
        },
      },
    }),
    {
      provide: AbstractSecurityStorage,
      useClass: DefaultLocalStorageService,
    },
    { provide: LOCAL_STORAGE_PORT, useValue: localStorage },
    {
      provide: LOGGING_CONFIG,
      useValue: LoggingLevel.Error,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: (loadConfig: LoadsConfigCommandPort) => {
        return () => loadConfig.loadConfig();
      },
      deps: [LOADS_CONFIG_COMMAND_PORT],
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: (googleService: GoogleService) => {
        return () => {
          googleService.setGTM('GTM-PQ69447');
        };
      },
      deps: [GoogleService],
      multi: true,
    },
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: PPS_PRIME_PRESET,
        options: {
          darkModeSelector: '.dark-theme',
        },
      },
    }),
  ],
})
export class AppModule {}
