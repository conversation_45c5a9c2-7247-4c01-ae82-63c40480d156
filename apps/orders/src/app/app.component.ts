import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Event, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router } from '@angular/router';
import { filter, map, Observable } from 'rxjs';

@Component({
  selector: 'esky-pps-nx-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent {
  loading$: Observable<boolean>;

  constructor(private readonly router: Router) {
    this.loading$ = this.router.events.pipe(
      filter(
        (e: Event) =>
          e instanceof NavigationStart ||
          e instanceof NavigationEnd ||
          e instanceof NavigationCancel ||
          e instanceof NavigationError,
      ),
      map((e: Event): boolean => e instanceof NavigationStart),
    );
  }
}
