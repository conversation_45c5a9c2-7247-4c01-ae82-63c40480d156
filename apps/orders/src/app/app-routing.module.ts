import {
  ActivitiesResol<PERSON>,
  AgentPackageDetailsResolver,
  ProcessesResolver,
  SetTitleToPackageNumberResolver,
} from '@agent-package';
import { loadingSmartAssistantActionsResolver } from '@ai';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { editionFeatureFlagGuard, loadFeatureFlagsResolver } from '@core/feature-flags';
import { developerModeResolver, setDevEnvTitleResolver } from '@developer-mode';
import {
  editAncillaryBookingResolver,
  editBookingBuyoutsResolver,
  editBookingPriceResolver,
  editContactDetailsResolver,
  editFlightBookingResolver,
  editHotelBookingResolver,
  editInsuranceBookingResolver,
  editInvoiceDetailsResolver,
  editOfferPricesResolver,
  editOrderServicesResolver,
  editPackageGuard,
  editPackageIssuingResolver,
  editPassengersResolver,
  editPaymentsResolver,
} from '@edit-package';
import { GeneralPackageDetailsResolver } from '@general-package-details';
import { importBookingGuard, importHotelBookingResolver, importInsuranceBookingResolver } from '@import-booking';
import { loadAssignedIssueResolver, loadIssuesResolver } from '@issues';
import { NotificationsResolver } from '@notifications';
import { PackageAvailabilityResolver } from '@package-availability';
import { loadUserAccountResolver } from '@user-account';
import { usersAccountsResolver } from '@users-accounts';
import { AutoLoginPartialRoutesGuard } from 'angular-auth-oidc-client';
import { ActivitiesPage } from './pages/activities/activities.page';
import { CallbackPage } from './pages/callback/callback.page';
import { EditBookingPage } from './pages/edit-booking/edit-booking.page';
import { ImportBookingPage } from './pages/import-booking/import-booking.page';
import { NotificationsPage } from './pages/notifications/notifications.page';
import { PackageDetailsPage } from './pages/package-details/package-details.page';
import { PackagesEmbeddedPage } from './pages/packages/packages-embedded.page';
import { PackagesPage } from './pages/packages/packages.page';
import { UnauthorizedPage } from './pages/unauthorized/unauthorized.page';
import { UserPage } from './pages/user/user.page';
import { UsersPage } from './pages/users/users.page';

const routes: Routes = [
  { path: '', redirectTo: '/orders', pathMatch: 'full' },
  {
    path: '',
    resolve: {
      developerMode: developerModeResolver,
    },
    children: [
      {
        path: 'orders',
        component: PackagesPage,
        canActivate: [AutoLoginPartialRoutesGuard],
        data: { title: 'PPS - Packages' },
        resolve: {
          envTitle: setDevEnvTitleResolver,
        },
      },
      {
        path: 'orders-embedded',
        component: PackagesEmbeddedPage,
        canActivate: [AutoLoginPartialRoutesGuard],
        title: 'PPS - Packages',
      },
      {
        path: 'orders/:packageNumber',
        redirectTo: 'orders/:packageNumber/details',
      },
      {
        path: 'orders/:packageNumber/details',
        runGuardsAndResolvers: 'always',
        component: PackageDetailsPage,
        resolve: {
          generalPackageDetails: GeneralPackageDetailsResolver,
          packageAvailability: PackageAvailabilityResolver,
          issues: loadIssuesResolver,
          featureFlags: loadFeatureFlagsResolver,
          packageDetails: AgentPackageDetailsResolver,
          title: SetTitleToPackageNumberResolver,
          envTitle: setDevEnvTitleResolver,
          activities: ActivitiesResolver,
          processes: ProcessesResolver,
          smartAssistantActions: loadingSmartAssistantActionsResolver,
        },
        canActivate: [AutoLoginPartialRoutesGuard],
      },
      {
        path: 'orders/:packageNumber/notifications',
        component: NotificationsPage,
        data: { title: 'PPS - Notifications' },
        resolve: {
          generalPackageDetails: GeneralPackageDetailsResolver,
          packageAvailability: PackageAvailabilityResolver,
          notifications: NotificationsResolver,
          envTitle: setDevEnvTitleResolver,
          issue: loadAssignedIssueResolver,
          featureFlags: loadFeatureFlagsResolver,
        },
        canActivate: [AutoLoginPartialRoutesGuard],
      },
      {
        path: 'orders/:packageNumber/preview',
        canActivate: [AutoLoginPartialRoutesGuard],
        resolve: {
          title: SetTitleToPackageNumberResolver,
          packageAvailability: PackageAvailabilityResolver,
          generalPackageDetails: GeneralPackageDetailsResolver,
          envTitle: setDevEnvTitleResolver,
          featureFlags: loadFeatureFlagsResolver,
          issue: loadAssignedIssueResolver,
        },
        children: [
          {
            path: 'offer-prices/:orderId',
            component: EditBookingPage,
            resolve: {
              editData: editOfferPricesResolver,
            },
            data: {
              editType: 'offer-prices',
              isPreviewMode: true,
            },
          },
          {
            path: 'offer-prices/:orderId',
            component: EditBookingPage,
            resolve: {
              editData: editOfferPricesResolver,
            },
            data: {
              editType: 'providerBookingPrices',
              isPreviewMode: true,
            },
          },
          {
            path: 'flight-booking/:productId/:itemId/:itemsCount/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'flight', isPreviewMode: true, editType: 'price-elements' },
          },
          {
            path: 'flight-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'flight', isPreviewMode: true, editType: 'price-elements' },
          },
          {
            path: 'hotel-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'hotel', isPreviewMode: true, editType: 'price-elements' },
          },
          {
            path: 'insurance-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'insurance', isPreviewMode: true, editType: 'price-elements' },
          },
          {
            path: 'ancillary-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'ancillary', isPreviewMode: true, editType: 'price-elements' },
          },
        ],
      },
      {
        path: 'orders/:packageNumber/edit',
        canActivate: [AutoLoginPartialRoutesGuard, editionFeatureFlagGuard, editPackageGuard],
        resolve: {
          title: SetTitleToPackageNumberResolver,
          packageAvailability: PackageAvailabilityResolver,
          generalPackageDetails: GeneralPackageDetailsResolver,
          issue: loadAssignedIssueResolver,
          envTitle: setDevEnvTitleResolver,
        },
        children: [
          {
            path: 'contact-details',
            component: EditBookingPage,
            resolve: {
              editData: editContactDetailsResolver,
            },
            data: { editType: 'contact' },
          },
          {
            path: 'invoice-details',
            component: EditBookingPage,
            resolve: {
              editData: editInvoiceDetailsResolver,
            },
            data: { editType: 'invoice' },
          },
          {
            path: 'offer-prices/:orderId',
            component: EditBookingPage,
            resolve: {
              editData: editOfferPricesResolver,
            },
            data: { editType: 'offer-prices' },
          },
          {
            path: 'order-services/:orderId',
            component: EditBookingPage,
            resolve: {
              editData: editOrderServicesResolver,
            },
            data: { editType: 'order-services' },
          },
          {
            path: 'passengers',
            component: EditBookingPage,
            resolve: {
              editData: editPassengersResolver,
            },
            data: { editType: 'packagePassengers' },
          },
          {
            path: 'payments/:orderId',
            component: EditBookingPage,
            resolve: {
              editData: editPaymentsResolver,
            },
            data: { editType: 'payments' },
          },
          {
            path: 'flight-booking/:productId/:itemId/:itemsCount/buyouts',
            component: EditBookingPage,
            resolve: {
              editData: editBookingBuyoutsResolver,
            },
            data: { productType: 'flight', editType: 'buyouts' },
          },
          {
            path: 'flight-booking/:productId/:itemId/:itemsCount/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'flight', editType: 'price-elements' },
          },
          {
            path: 'flight-booking/:productId/:itemId/:itemsCount/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editFlightBookingResolver,
            },
            data: { productType: 'flight' },
          },
          {
            path: 'flight-booking/:productId/:itemId/buyouts',
            component: EditBookingPage,
            resolve: {
              editData: editBookingBuyoutsResolver,
            },
            data: { productType: 'flight', editType: 'buyouts' },
          },
          {
            path: 'flight-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'flight', editType: 'price-elements' },
          },
          {
            path: 'flight-booking/:productId/:itemId/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editFlightBookingResolver,
            },
            data: { productType: 'flight' },
          },
          {
            path: 'hotel-booking/:productId/:itemId/buyouts',
            component: EditBookingPage,
            resolve: {
              editData: editBookingBuyoutsResolver,
            },
            data: { productType: 'hotel', editType: 'buyouts' },
          },
          {
            path: 'hotel-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'hotel', editType: 'price-elements' },
          },
          {
            path: 'hotel-booking/:productId/:itemId/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editHotelBookingResolver,
            },
            data: { productType: 'hotel' },
          },
          {
            path: 'insurance-booking/:productId/:itemId/buyouts',
            component: EditBookingPage,
            resolve: {
              editData: editBookingBuyoutsResolver,
            },
            data: { productType: 'insurance', editType: 'buyouts' },
          },
          {
            path: 'insurance-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'insurance', editType: 'price-elements' },
          },
          {
            path: 'insurance-booking/:productId/:itemId/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editInsuranceBookingResolver,
            },
            data: { productType: 'insurance' },
          },
          {
            path: 'ancillary-booking/:productId/:itemId/buyouts',
            component: EditBookingPage,
            resolve: {
              editData: editBookingBuyoutsResolver,
            },
            data: { productType: 'ancillary', editType: 'buyouts' },
          },
          {
            path: 'ancillary-booking/:productId/:itemId/price-elements',
            component: EditBookingPage,
            resolve: {
              editData: editBookingPriceResolver,
            },
            data: { productType: 'ancillary', editType: 'price-elements' },
          },
          {
            path: 'ancillary-booking/:productId/:itemId/:productSectionType',
            component: EditBookingPage,
            resolve: {
              editData: editAncillaryBookingResolver,
            },
            data: { productType: 'ancillary' },
          },
          {
            path: 'package-issuing',
            component: EditBookingPage,
            resolve: {
              editData: editPackageIssuingResolver,
            },
            data: { editType: 'package-issuing' },
          },
        ],
      },
      {
        path: 'orders/:packageNumber/import-booking/:importId',
        canActivate: [AutoLoginPartialRoutesGuard, editionFeatureFlagGuard, importBookingGuard],
        resolve: {
          title: SetTitleToPackageNumberResolver,
          packageAvailability: PackageAvailabilityResolver,
          generalPackageDetails: GeneralPackageDetailsResolver,
          envTitle: setDevEnvTitleResolver,
          issue: loadAssignedIssueResolver,
        },
        children: [
          {
            path: 'hotel',
            component: ImportBookingPage,
            resolve: {
              importBooking: importHotelBookingResolver,
            },
            data: { productType: 'hotel' },
          },
          {
            path: 'insurance',
            component: ImportBookingPage,
            resolve: {
              importBooking: importInsuranceBookingResolver,
            },
            data: { productType: 'insurance' },
          },
        ],
      },
      {
        path: 'orders/:packageNumber/activities',
        component: ActivitiesPage,
        resolve: {
          activities: ActivitiesResolver,
          envTitle: setDevEnvTitleResolver,
        },
        canActivate: [AutoLoginPartialRoutesGuard],
      },
      {
        path: 'users',
        component: UsersPage,
        data: { title: 'PPS - Users' },
        resolve: {
          usersAccounts: usersAccountsResolver,
          envTitle: setDevEnvTitleResolver,
        },
        canActivate: [AutoLoginPartialRoutesGuard],
      },
      {
        path: 'users/:userId/details',
        component: UserPage,
        data: { title: 'PPS - Users' },
        resolve: {
          userAccount: loadUserAccountResolver,
          envTitle: setDevEnvTitleResolver,
        },
        canActivate: [AutoLoginPartialRoutesGuard],
      },
    ],
  },
  { path: 'callback', component: CallbackPage },
  { path: 'unauthorized', component: UnauthorizedPage },
  { path: '**', redirectTo: '/orders', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { anchorScrolling: 'enabled' })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
