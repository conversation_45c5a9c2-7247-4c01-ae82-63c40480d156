import { DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnDestroy, OnInit } from '@angular/core';

@Component({
  selector: 'esky-pps-nx-activities-page',
  templateUrl: 'activities.page.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivitiesPage implements OnInit, OnDestroy {
  private readonly scriptId = 'iframe-resizer';

  constructor(@Inject(DOCUMENT) private readonly docRef: Document) {}

  ngOnInit(): void {
    const scriptEl = this.docRef.createElement('script');
    scriptEl.src = 'assets/js/iframeResizer.contentWindow.min.js';
    scriptEl.id = this.scriptId;
    this.docRef.body.appendChild(scriptEl);
  }

  ngOnDestroy(): void {
    this.docRef.getElementById(this.scriptId)?.remove();
  }
}
