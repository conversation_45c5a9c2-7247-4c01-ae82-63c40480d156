import { DOCUMENT } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivitiesPage } from './activities.page';

describe('ActivitiesPage', () => {
  let component: ActivitiesPage;
  let fixture: ComponentFixture<ActivitiesPage>;
  let documentSpy: Document;

  beforeEach(async () => {
    documentSpy = document;

    await TestBed.configureTestingModule({
      declarations: [ActivitiesPage],
      providers: [{ provide: DOCUMENT, useValue: documentSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(ActivitiesPage);
    component = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('should append the script to the document body with correct attributes', () => {
      const scriptId = component['scriptId'];
      component.ngOnInit();

      const scriptEl = documentSpy.getElementById(scriptId) as HTMLScriptElement;
      expect(scriptEl).toBeTruthy();
      expect(scriptEl.src).toContain('assets/js/iframeResizer.contentWindow.min.js');
      expect(scriptEl.id).toBe(scriptId);
    });
  });

  describe('ngOnDestroy', () => {
    it('should remove the script from the document body', () => {
      component.ngOnInit();
      component.ngOnDestroy();

      const scriptEl = documentSpy.getElementById(component['scriptId']);
      expect(scriptEl).toBeNull();
    });
  });
});
