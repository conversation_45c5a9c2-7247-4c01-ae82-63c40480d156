import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DeveloperModeComponent } from '@developer-mode';
import {
  GraphqlUserAccountServiceModule,
  InMemoryUserAccountDetailsStorageModule,
  UserAccountDetailsComponent,
  UserAccountStateModule,
  UserPackagesListComponent,
} from '@user-account';
import { UserPage } from './user.page';

@NgModule({
  imports: [
    CommonModule,
    DeveloperModeComponent,
    GraphqlUserAccountServiceModule,
    InMemoryUserAccountDetailsStorageModule,
    UserAccountDetailsComponent,
    UserPackagesListComponent,
    UserAccountStateModule,
  ],
  declarations: [UserPage],
  providers: [],
  exports: [],
})
export class UserPageModule {}
