.wrapper {
  background-color: var(--background-color);
  min-height: 100%;

  .search-box {
    width: 225px;
  }

  .toolbar {
    display: flex;
    align-items: center;
    width: 100%;
    height: 64px;
    padding: 0 var(--spacing-xl);
    background-color: var(--color-white);

    .title {
      flex: 1;
      cursor: pointer;
      font-size: var(--font-size-l);
      line-height: 1.5;
      font-weight: var(--font-weight-bold);
      color: var(--color-primary-50);
    }
  }

  .staging {
    background: var(--staging-page-background-color);
  }

  .content {
    padding: var(--spacing-m) var(--spacing-xl);

    .title {
      font-size: var(--font-size-l);
      line-height: 1.5;
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--spacing-m);
    }
  }
}

.top {
  position: fixed;
  width: 100%;
  z-index: 5;
}
