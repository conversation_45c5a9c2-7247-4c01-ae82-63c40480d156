import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterLink } from '@angular/router';
import { UserComponentModule } from '@core/auth';
import { DomainUrlPipeModule } from '@core/config';
import { DeveloperModeComponent } from '@developer-mode';
import { CoreAngularModule } from '@ecs/angular';
import {
  EnvBasedClassDirectiveModule,
  HttpPackagesServiceModule,
  InMemoryPackagesStorageModule,
  PackagesPaginationComponentModule,
  PackagesSearchBoxDirectiveModule,
  PackagesStateModule,
  PackagesTableComponentModule,
  SearchInstructionComponent,
} from '@packages';
import { LoadingUiServiceModule } from '@shared/loading';
import { UiSearchBoxComponentModule } from '@shared/ui';
import { PackagesEmbeddedPage } from './packages-embedded.page';
import { PackagesPage } from './packages.page';

@NgModule({
  imports: [
    CommonModule,
    CoreAngularModule,
    DomainUrlPipeModule,
    EnvBasedClassDirectiveModule,
    HttpPackagesServiceModule,
    InMemoryPackagesStorageModule,
    LoadingUiServiceModule,
    PackagesSearchBoxDirectiveModule,
    PackagesStateModule,
    PackagesPaginationComponentModule,
    PackagesTableComponentModule,
    SearchInstructionComponent,
    UiSearchBoxComponentModule,
    UserComponentModule,
    DeveloperModeComponent,
    RouterLink,
  ],
  declarations: [PackagesPage, PackagesEmbeddedPage],
  providers: [],
  exports: [],
})
export class PackagesPageModule {}
