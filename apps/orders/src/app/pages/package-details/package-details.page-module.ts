import {
  ActivitiesComponent,
  ActivitiesStateModule,
  AgentPackageDetailsResolverModule,
  AncillaryBookingsComponent,
  AssignedIssueChangedEventHandlerModule as AssignedIssueChangedEventHandlerModuleAgentPackage,
  ContactDetailsComponent,
  FlightBookingsComponentModule,
  GraphqlActivitiesServiceModule,
  GraphqlOrderServiceModule,
  GraphqlPackageDetailsServiceModule,
  GraphqlPaymentSessionServiceModule,
  HotelBookingsComponentModule,
  InMemoryActivitiesStorageModule,
  InMemoryPackageDetailsStorageModule,
  InMemoryProcessesStorageModule,
  InsuranceBookingsComponentModule,
  InvoiceDetailsComponent,
  OperationsServiceModule,
  PackageDetailsStateModule,
  PaymentPlanComponent,
  ProcessesComponent,
  ProcessesResolverModule,
  ProcessesStateModule,
  SetTitleToPackageNumberResolverModule,
  StatusesComponentModule,
  TransactionsComponent,
} from '@agent-package';
import {
  AiEventsModule,
  AiSmartAssistantComponent,
  AiStateModule,
  GraphqlSmartAssistantServiceModule,
  InMemoryAiStorageModule,
  RequestAiSmartAssistantResponseEventHandlerModule,
} from '@ai';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DeveloperModeComponent, DeveloperModeStateModule, InMemoryDeveloperModeStorageModule } from '@developer-mode';
import {
  AssignedIssueChangedEventHandlerModule as AssignedIssueChangedEventHandlerModuleGenInfoLib,
  GeneralPackageDetailsComponent,
  GeneralPackageDetailsResolverModule,
  GeneralPackageDetailsStateModule,
  GraphqlGeneralPackageDetailsServiceModule,
  GraphqlPackageTagsServiceModule,
  InMemoryGeneralPackageDetailsStorageModule,
  LoadingSmartAssistantActionsEventHandlerModule,
  UpdatePackageTagsComponent,
} from '@general-package-details';
import {
  GraphqlImportBookingServiceModule,
  GraphqlImportOfferServiceModule,
  ImportBookingDialogComponent,
  ImportBookingStateModule,
  ImportOfferDialogComponent,
  InMemoryImportBookingStorageModule,
  ShowImportBookingDialogRequestEventHandlerModule,
  ShowImportOfferDialogRequestedEventHandlerModule,
} from '@import-booking';
import {
  CategoriesServiceModule,
  CreateIssueDialogComponent,
  InMemoryIssuesStorageModule,
  IssuesEventsModule,
  IssuesServiceModule,
  IssuesStateModule,
  IssuesTabsComponent,
  IssueTopbarComponent,
  PackageAvailableLoadedEventHandlerModule,
  ShowCreateDialogRequestEventHandlerModule,
} from '@issues';
import {
  EmailPreviewDialogComponent,
  GraphqlNotificationsServiceModule,
  InMemoryNotificationsStorageModule,
  NotificationsStateModule,
} from '@notifications';
import {
  GraphqlPackageAvailabilityServiceModule,
  InMemoryPackageAvailabilityStorageModule,
  NotAvailableComponent,
  PackageAvailabilityEventsModule,
  PackageAvailabilityResolverModule,
  PackageAvailabilityStateModule,
} from '@package-availability';
import { BodyServiceModule } from '@shared/ui';
import { PackageDetailsPage } from './package-details.page';

@NgModule({
  imports: [
    CommonModule,
    ContactDetailsComponent,
    DeveloperModeComponent,
    DeveloperModeStateModule,
    StatusesComponentModule,
    GraphqlActivitiesServiceModule,
    GraphqlGeneralPackageDetailsServiceModule,
    GraphqlPackageAvailabilityServiceModule,
    GraphqlPackageDetailsServiceModule,
    GraphqlPaymentSessionServiceModule,
    GraphqlOrderServiceModule,
    InvoiceDetailsComponent,
    OperationsServiceModule,
    GeneralPackageDetailsResolverModule,
    GeneralPackageDetailsStateModule,
    PackageAvailabilityResolverModule,
    AgentPackageDetailsResolverModule,
    SetTitleToPackageNumberResolverModule,
    InMemoryDeveloperModeStorageModule,
    InMemoryPackageDetailsStorageModule,
    InMemoryActivitiesStorageModule,
    InMemoryGeneralPackageDetailsStorageModule,
    InMemoryPackageAvailabilityStorageModule,
    PackageDetailsStateModule,
    PackageAvailabilityStateModule,
    ActivitiesStateModule,
    GeneralPackageDetailsComponent,
    FlightBookingsComponentModule,
    HotelBookingsComponentModule,
    InsuranceBookingsComponentModule,
    AncillaryBookingsComponent,
    BodyServiceModule,
    NotAvailableComponent,
    ActivitiesComponent,
    TransactionsComponent,
    PaymentPlanComponent,
    CreateIssueDialogComponent,
    IssuesTabsComponent,
    IssueTopbarComponent,
    IssuesEventsModule,
    IssuesServiceModule,
    CategoriesServiceModule,
    GraphqlImportBookingServiceModule,
    AssignedIssueChangedEventHandlerModuleAgentPackage,
    AssignedIssueChangedEventHandlerModuleGenInfoLib,
    ShowCreateDialogRequestEventHandlerModule,
    ShowImportBookingDialogRequestEventHandlerModule,
    PackageAvailabilityEventsModule,
    PackageAvailableLoadedEventHandlerModule,
    IssuesStateModule,
    ImportBookingStateModule,
    InMemoryIssuesStorageModule,
    InMemoryImportBookingStorageModule,
    GeneralPackageDetailsStateModule,
    ImportBookingDialogComponent,
    EmailPreviewDialogComponent,
    NotificationsStateModule,
    GraphqlNotificationsServiceModule,
    InMemoryNotificationsStorageModule,
    InMemoryProcessesStorageModule,
    ProcessesComponent,
    ProcessesResolverModule,
    ProcessesStateModule,
    AiSmartAssistantComponent,
    AiStateModule,
    InMemoryAiStorageModule,
    RequestAiSmartAssistantResponseEventHandlerModule,
    GraphqlSmartAssistantServiceModule,
    AiEventsModule,
    LoadingSmartAssistantActionsEventHandlerModule,
    GraphqlPackageTagsServiceModule,
    UpdatePackageTagsComponent,
    GraphqlImportOfferServiceModule,
    ShowImportOfferDialogRequestedEventHandlerModule,
    ImportOfferDialogComponent,
  ],
  declarations: [PackageDetailsPage],
  providers: [],
  exports: [],
})
export class PackageDetailsPageModule {}
