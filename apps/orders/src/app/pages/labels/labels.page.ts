import { ChangeDetectionStrategy, Component } from '@angular/core';

interface VerificationResults {
  validCount: number;
  invalidCount: number;
  validPackages: string[];
  invalidPackages: string[];
}

@Component({
  templateUrl: 'labels.page.html',
  styleUrls: ['labels.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LabelsPage {
  // Form data
  packageNumbers = '';
  selectedFileName = '';
  labelHandling: 'add' | 'replace' | 'remove' = 'add';
  selectedLabel = '';
  labelDescription = '';

  // UI state
  showVerificationStep = false;
  showValidPackages = false;
  showInvalidPackages = false;
  showSuccessMessage = false;
  appliedCount = 0;

  // Verification results
  verificationResults: VerificationResults | null = null;

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFileName = input.files[0].name;
      // TODO: Process CSV file
      const file = input.files[0];
      this.readCSVFile(file);
    }
  }

  private readCSVFile(file: File): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      // Simple CSV parsing - assumes one package number per line
      const packageNumbers = text.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
      this.packageNumbers = packageNumbers.join('\n');
    };
    reader.readAsText(file);
  }

  hasPackageNumbers(): boolean {
    return this.packageNumbers.trim().length > 0;
  }

  verifyNumbers(): void {
    if (!this.hasPackageNumbers()) return;

    // Parse package numbers from textarea
    const numbers = this.packageNumbers
      .split(/[,\n]/)
      .map(num => num.trim())
      .filter(num => num.length > 0);

    // Mock verification - in real app this would call a service
    const validPackages: string[] = [];
    const invalidPackages: string[] = [];

    numbers.forEach(num => {
      // Simple validation - check if it's a number
      if (/^\d+$/.test(num)) {
        validPackages.push(num);
      } else {
        invalidPackages.push(num);
      }
    });

    this.verificationResults = {
      validCount: validPackages.length,
      invalidCount: invalidPackages.length,
      validPackages,
      invalidPackages
    };

    this.showVerificationStep = true;
  }

  toggleValidPackages(): void {
    this.showValidPackages = !this.showValidPackages;
  }

  toggleInvalidPackages(): void {
    this.showInvalidPackages = !this.showInvalidPackages;
  }

  canApplyLabels(): boolean {
    if (!this.verificationResults || this.verificationResults.validCount === 0) {
      return false;
    }

    if (this.labelHandling === 'remove') {
      return true;
    }

    return this.selectedLabel.length > 0;
  }

  getApplyButtonText(): string {
    if (this.labelHandling === 'remove') {
      return 'Remove all existing labels';
    }
    return `Apply label(s) to ${this.verificationResults?.validCount || 0} packages`;
  }

  applyLabels(): void {
    if (!this.canApplyLabels()) return;

    // Mock application - in real app this would call a service
    this.appliedCount = this.verificationResults?.validCount || 0;
    this.showSuccessMessage = true;
    this.showVerificationStep = false;

    // Auto-hide success message after 5 seconds
    setTimeout(() => {
      this.showSuccessMessage = false;
    }, 5000);
  }

  clearForm(): void {
    this.packageNumbers = '';
    this.selectedFileName = '';
    this.labelHandling = 'add';
    this.selectedLabel = '';
    this.labelDescription = '';
    this.showVerificationStep = false;
    this.showValidPackages = false;
    this.showInvalidPackages = false;
    this.showSuccessMessage = false;
    this.verificationResults = null;
    this.appliedCount = 0;
  }
}
