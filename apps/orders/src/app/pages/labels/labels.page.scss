.container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding: var(--spacing-xl);
}

.content {
  max-width: var(--page-max-width);
  margin: var(--spacing-m) auto;
}

.top {
  position: fixed;
  width: 100%;
  z-index: 5;
}

.header {
  margin-bottom: var(--spacing-xl);

  .breadcrumb {
    color: var(--color-primary-50);
    text-decoration: none;
    font-size: var(--font-size-s);
    margin-bottom: var(--spacing-xs);
    display: inline-block;

    &:hover {
      text-decoration: underline;
    }
  }

  h1 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--color-text-primary);
  }

  .subtitle {
    color: var(--color-text-secondary);
    margin: 0;
    font-size: var(--font-size-m);
  }
}

.step-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-l);
}

.step-card {
  background: var(--color-white);
  border-radius: var(--border-radius-m);
  padding: var(--spacing-xl);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    font-size: var(--font-size-l);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--spacing-m) 0;
    color: var(--color-text-primary);
  }

  h3 {
    font-size: var(--font-size-m);
    font-weight: var(--font-weight-semibold);
    margin: 0 0 var(--spacing-s) 0;
    color: var(--color-text-primary);
  }

  .step-description {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-l);
    line-height: 1.5;
  }
}

.input-section {
  margin-bottom: var(--spacing-l);

  label {
    display: block;
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
    color: var(--color-text-primary);
  }

  .package-input {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-m);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-s);
    font-family: inherit;
    font-size: var(--font-size-m);
    resize: vertical;

    &:focus {
      outline: none;
      border-color: var(--color-primary-50);
      box-shadow: 0 0 0 2px rgba(var(--color-primary-50-rgb), 0.2);
    }
  }

  .help-text {
    font-size: var(--font-size-s);
    color: var(--color-text-secondary);
    margin-top: var(--spacing-xs);
    margin-bottom: 0;
  }
}

.divider {
  text-align: center;
  margin: var(--spacing-l) 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--color-border);
  }

  span {
    background: var(--color-white);
    padding: 0 var(--spacing-m);
    color: var(--color-text-secondary);
    font-size: var(--font-size-s);
  }
}

.upload-section {
  margin-bottom: var(--spacing-l);

  .file-upload {
    display: flex;
    align-items: center;
    gap: var(--spacing-m);
    margin-bottom: var(--spacing-s);

    input[type="file"] {
      display: none;
    }

    .choose-file-btn {
      background: var(--color-primary-50);
      color: var(--color-white);
      border: none;
      padding: var(--spacing-s) var(--spacing-m);
      border-radius: var(--border-radius-s);
      cursor: pointer;
      font-size: var(--font-size-m);

      &:hover {
        background: var(--color-primary-60);
      }
    }

    .file-name {
      color: var(--color-text-secondary);
      font-size: var(--font-size-m);
    }
  }

  .download-template {
    color: var(--color-primary-50);
    text-decoration: none;
    font-size: var(--font-size-s);

    &:hover {
      text-decoration: underline;
    }
  }
}

.verify-btn {
  background: var(--color-primary-50);
  color: var(--color-white);
  border: none;
  padding: var(--spacing-m) var(--spacing-l);
  border-radius: var(--border-radius-s);
  cursor: pointer;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-semibold);

  &:hover:not(:disabled) {
    background: var(--color-primary-60);
  }

  &:disabled {
    background: var(--color-gray-30);
    cursor: not-allowed;
  }
}

.verification-results {
  margin-bottom: var(--spacing-l);

  .result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-s);
    padding: var(--spacing-m);
    border-radius: var(--border-radius-s);
    margin-bottom: var(--spacing-s);

    .icon {
      font-weight: bold;
      font-size: var(--font-size-l);
    }

    &.success {
      background: rgba(34, 197, 94, 0.1);
      color: rgb(21, 128, 61);
      border: 1px solid rgba(34, 197, 94, 0.3);

      .icon {
        color: rgb(34, 197, 94);
      }
    }

    &.error {
      background: rgba(239, 68, 68, 0.1);
      color: rgb(153, 27, 27);
      border: 1px solid rgba(239, 68, 68, 0.3);

      .icon {
        color: rgb(239, 68, 68);
      }
    }
  }
}

.expandable-section {
  margin-bottom: var(--spacing-m);

  .expand-btn {
    background: none;
    border: none;
    color: var(--color-primary-50);
    cursor: pointer;
    font-size: var(--font-size-m);
    display: flex;
    align-items: center;
    gap: var(--spacing-s);
    padding: var(--spacing-s) 0;

    &:hover {
      text-decoration: underline;
    }

    .arrow {
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .package-list {
    background: var(--color-gray-05);
    border-radius: var(--border-radius-s);
    padding: var(--spacing-m);
    max-height: 200px;
    overflow-y: auto;

    .package-item {
      padding: var(--spacing-xs) 0;
      font-family: monospace;
      font-size: var(--font-size-s);

      &.invalid {
        color: rgb(153, 27, 27);
      }
    }
  }
}

.label-configuration {
  .radio-group {
    margin-bottom: var(--spacing-l);

    .radio-option {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-s);
      margin-bottom: var(--spacing-m);
      cursor: pointer;

      input[type="radio"] {
        margin-top: 2px;
      }

      .radio-text {
        flex: 1;
        font-size: var(--font-size-m);
        color: var(--color-text-primary);
      }

      .warning-text {
        display: block;
        font-size: var(--font-size-s);
        color: rgb(153, 27, 27);
        margin-top: var(--spacing-xs);
      }
    }
  }

  .label-config {
    margin-bottom: var(--spacing-l);

    .form-group {
      margin-bottom: var(--spacing-m);

      label {
        display: block;
        font-weight: var(--font-weight-semibold);
        margin-bottom: var(--spacing-xs);
        color: var(--color-text-primary);
      }

      .label-select {
        width: 100%;
        padding: var(--spacing-m);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-s);
        font-size: var(--font-size-m);
        background: var(--color-white);

        &:focus {
          outline: none;
          border-color: var(--color-primary-50);
          box-shadow: 0 0 0 2px rgba(var(--color-primary-50-rgb), 0.2);
        }
      }

      .description-input {
        width: 100%;
        min-height: 80px;
        padding: var(--spacing-m);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-s);
        font-family: inherit;
        font-size: var(--font-size-m);
        resize: vertical;

        &:focus {
          outline: none;
          border-color: var(--color-primary-50);
          box-shadow: 0 0 0 2px rgba(var(--color-primary-50-rgb), 0.2);
        }
      }

      .char-count {
        font-size: var(--font-size-s);
        color: var(--color-text-secondary);
        margin-top: var(--spacing-xs);
      }
    }

    .add-label-btn {
      background: none;
      border: none;
      color: var(--color-primary-50);
      cursor: pointer;
      font-size: var(--font-size-m);
      text-decoration: underline;

      &:hover {
        color: var(--color-primary-60);
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: var(--spacing-m);

    .apply-btn {
      background: var(--color-primary-50);
      color: var(--color-white);
      border: none;
      padding: var(--spacing-m) var(--spacing-l);
      border-radius: var(--border-radius-s);
      cursor: pointer;
      font-size: var(--font-size-m);
      font-weight: var(--font-weight-semibold);

      &:hover:not(:disabled) {
        background: var(--color-primary-60);
      }

      &:disabled {
        background: var(--color-gray-30);
        cursor: not-allowed;
      }
    }

    .clear-btn {
      background: none;
      border: 1px solid var(--color-border);
      color: var(--color-text-primary);
      padding: var(--spacing-m) var(--spacing-l);
      border-radius: var(--border-radius-s);
      cursor: pointer;
      font-size: var(--font-size-m);

      &:hover {
        background: var(--color-gray-05);
      }
    }
  }
}

.success-message {
  .success-banner {
    background: rgba(34, 197, 94, 0.1);
    color: rgb(21, 128, 61);
    border: 1px solid rgba(34, 197, 94, 0.3);
    padding: var(--spacing-m);
    border-radius: var(--border-radius-s);
    display: flex;
    align-items: center;
    gap: var(--spacing-s);

    .success-icon {
      font-weight: bold;
      font-size: var(--font-size-l);
      color: rgb(34, 197, 94);
    }
  }
}
