<div class="top">
  <lib-developer-mode />
</div>
<div class="container">
  <div class="content">
    <div class="header">
      <a routerLink="/orders" class="breadcrumb">Post Sale</a>
      <h1>Mass packages labeling</h1>
      <p class="subtitle">Apply labels to packages</p>
    </div>

    <div class="step-container">
      <!-- Step 1: Provide package numbers -->
      <div class="step-card">
        <h2>Provide package numbers</h2>
        <p class="step-description">Use this page to apply labels to multiple packages at once. You can provide the package numbers by typing/pasting them below or by uploading a CSV file.</p>

        <div class="input-section">
          <h3>Option 1: Enter numbers manually</h3>
          <label for="package-numbers">Package numbers:</label>
          <textarea
            id="package-numbers"
            class="package-input"
            placeholder="Enter package numbers, one per line, or separated by commas."
            [(ngModel)]="packageNumbers"
            rows="6">
          </textarea>
          <p class="help-text">E.g.: 1234567, 7654321, 9876543 or list each number on a new line</p>
        </div>

        <div class="divider">
          <span>OR</span>
        </div>

        <div class="upload-section">
          <h3>Option 2: Upload a file</h3>
          <label for="csv-file">Select CSV File:</label>
          <div class="file-upload">
            <input
              type="file"
              id="csv-file"
              accept=".csv"
              (change)="onFileSelected($event)"
              #fileInput>
            <button
              type="button"
              class="choose-file-btn"
              (click)="fileInput.click()">
              Choose file
            </button>
            <span class="file-name">{{ selectedFileName || 'No file chosen' }}</span>
          </div>
          <p class="help-text">Accepted format: .csv. File should contain one reservation number per row in a single column</p>
          <a href="#" class="download-template">Download CSV template ↓</a>
        </div>

        <button
          type="button"
          class="verify-btn"
          [disabled]="!hasPackageNumbers()"
          (click)="verifyNumbers()">
          Verify numbers and proceed
        </button>
      </div>

      <!-- Step 2: Verification results and label configuration -->
      <div class="step-card" *ngIf="showVerificationStep">
        <h2>Verification results and label configuration</h2>

        <div class="verification-results" *ngIf="verificationResults">
          <h3>Verification results:</h3>

          <div class="result-item success" *ngIf="verificationResults.validCount > 0">
            <span class="icon">✓</span>
            Found {{ verificationResults.validCount }} valid package(s) ready for labeling
          </div>

          <div class="result-item error" *ngIf="verificationResults.invalidCount > 0">
            <span class="icon">✗</span>
            Found {{ verificationResults.invalidCount }} number(s) that were invalid or not found
          </div>

          <div class="expandable-section">
            <button type="button" class="expand-btn" (click)="toggleValidPackages()">
              Valid packages list
              <span class="arrow" [class.expanded]="showValidPackages">▼</span>
            </button>
            <div class="package-list" *ngIf="showValidPackages">
              <div *ngFor="let pkg of verificationResults.validPackages" class="package-item">
                {{ pkg }}
              </div>
            </div>
          </div>

          <div class="expandable-section" *ngIf="verificationResults.invalidCount > 0">
            <button type="button" class="expand-btn" (click)="toggleInvalidPackages()">
              Invalid or not found numbers
              <span class="arrow" [class.expanded]="showInvalidPackages">▼</span>
            </button>
            <div class="package-list" *ngIf="showInvalidPackages">
              <div *ngFor="let pkg of verificationResults.invalidPackages" class="package-item invalid">
                {{ pkg }}
              </div>
            </div>
          </div>
        </div>

        <div class="label-configuration">
          <h3>How to handle existing labels:</h3>
          <div class="radio-group">
            <label class="radio-option">
              <input type="radio" name="labelHandling" value="add" [(ngModel)]="labelHandling">
              <span class="radio-text">Add selected label(s), keeping all existing labels</span>
            </label>
            <label class="radio-option">
              <input type="radio" name="labelHandling" value="replace" [(ngModel)]="labelHandling">
              <span class="radio-text">Replace all existing labels with only the selected label(s)</span>
              <span class="warning-text">Warning: This will remove all existing labels from these reservations.</span>
            </label>
            <label class="radio-option">
              <input type="radio" name="labelHandling" value="remove" [(ngModel)]="labelHandling">
              <span class="radio-text">Remove all existing labels (without adding new ones)</span>
            </label>
          </div>

          <div class="label-config" *ngIf="labelHandling !== 'remove'">
            <h3>Label configuration</h3>
            <div class="form-group">
              <label for="label-select">Select label</label>
              <select id="label-select" [(ngModel)]="selectedLabel" class="label-select">
                <option value="">No selected label</option>
                <option value="critical">Critical</option>
              </select>
            </div>

            <div class="form-group">
              <label for="description">Description</label>
              <textarea
                id="description"
                class="description-input"
                placeholder="Describe shortly situation (max. 240 characters)"
                [(ngModel)]="labelDescription"
                maxlength="240">
              </textarea>
              <span class="char-count">(Optional)</span>
            </div>

            <button type="button" class="add-label-btn">Add another label</button>
          </div>

          <div class="action-buttons">
            <button
              type="button"
              class="apply-btn"
              [disabled]="!canApplyLabels()"
              (click)="applyLabels()">
              {{ getApplyButtonText() }}
            </button>
            <button type="button" class="clear-btn" (click)="clearForm()">Clear</button>
          </div>
        </div>
      </div>

      <!-- Step 3: Success message -->
      <div class="success-message" *ngIf="showSuccessMessage">
        <div class="success-banner">
          <span class="success-icon">✓</span>
          Action successful: Label(s) applied to {{ appliedCount }} packages
        </div>
      </div>
    </div>
  </div>
</div>
