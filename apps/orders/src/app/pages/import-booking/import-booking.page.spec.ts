import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { ImportBookingPage } from './import-booking.page';

describe('ImportBookingPage', () => {
  const given = async (data: Partial<ActivatedRoute>) => {
    await TestBed.configureTestingModule({
      declarations: [ImportBookingPage],
      providers: [{ provide: ActivatedRoute, useValue: data }],
    }).compileComponents();

    const fixture = TestBed.createComponent(ImportBookingPage);
    const component = fixture.componentInstance;

    return { component };
  };

  [
    {
      whenData: {
        activatedRouteStub: {
          snapshot: { data: { wrongName: '__PRODUCT_TYPE__' } },
        },
      },
      thenData: {
        productType: '',
      },
    },
    {
      whenData: {
        activatedRouteStub: {
          snapshot: { data: { productType: '__PRODUCT_TYPE__' } },
        },
      },
      thenData: {
        productType: '__PRODUCT_TYPE__',
      },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should initialize its parameters ${i + 1}`, async () => {
      const { component } = await given(whenData.activatedRouteStub as unknown as ActivatedRoute);

      expect(component.productType).toBe(thenData.productType);
    }),
  );
});
