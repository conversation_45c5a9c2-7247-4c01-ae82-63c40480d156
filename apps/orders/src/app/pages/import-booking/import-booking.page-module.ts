import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  GeneralPackageDetailsComponent,
  GeneralPackageDetailsResolverModule,
  GeneralPackageDetailsStateModule,
  GraphqlGeneralPackageDetailsServiceModule,
  InMemoryGeneralPackageDetailsStorageModule,
} from '@general-package-details';
import {
  GraphqlImportBookingServiceModule,
  ImportBookingStateModule,
  ImportHotelBookingDraftComponent,
  ImportInsuranceBookingDraftComponent,
  InMemoryImportBookingStorageModule,
} from '@import-booking';
import { InMemoryIssuesStorageModule, IssuesStateModule, IssueTopbarComponent } from '@issues';
import {
  GraphqlPackageAvailabilityServiceModule,
  InMemoryPackageAvailabilityStorageModule,
  NotAvailableComponent,
  PackageAvailabilityEventsModule,
  PackageAvailabilityResolverModule,
  PackageAvailabilityStateModule,
} from '@package-availability';
import { ImportBookingPage } from './import-booking.page';

@NgModule({
  imports: [
    CommonModule,
    IssueTopbarComponent,
    InMemoryIssuesStorageModule,
    IssuesStateModule,
    NotAvailableComponent,
    GraphqlPackageAvailabilityServiceModule,
    InMemoryPackageAvailabilityStorageModule,
    PackageAvailabilityEventsModule,
    PackageAvailabilityResolverModule,
    PackageAvailabilityStateModule,
    GeneralPackageDetailsComponent,
    GeneralPackageDetailsResolverModule,
    GeneralPackageDetailsStateModule,
    GraphqlGeneralPackageDetailsServiceModule,
    InMemoryGeneralPackageDetailsStorageModule,
    ImportHotelBookingDraftComponent,
    ImportInsuranceBookingDraftComponent,
    GraphqlImportBookingServiceModule,
    ImportBookingStateModule,
    InMemoryImportBookingStorageModule,
  ],
  declarations: [ImportBookingPage],
  providers: [],
  exports: [],
})
export class ImportBookingPageModule {}
