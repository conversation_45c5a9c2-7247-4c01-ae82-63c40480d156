import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  templateUrl: 'import-booking.page.html',
  styleUrls: ['import-booking.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImportBookingPage {
  productType: string;

  constructor(private readonly route: ActivatedRoute) {
    this.productType = this.route.snapshot.data['productType'] ?? '';
  }
}
