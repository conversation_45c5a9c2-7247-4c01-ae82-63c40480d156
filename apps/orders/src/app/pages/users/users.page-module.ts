import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DeveloperModeComponent } from '@developer-mode';
import { CoreAngularModule } from '@ecs/angular';
import { UserAccountDetailsComponent } from '@user-account';
import {
  GraphqlUsersAccountsServiceModule,
  InMemoryUsersAccountsStorageModule,
  UsersAccountsStateModule,
  UsersListComponent,
} from '@users-accounts';
import { UsersPage } from './users.page';

@NgModule({
  imports: [
    CommonModule,
    CoreAngularModule,
    DeveloperModeComponent,
    GraphqlUsersAccountsServiceModule,
    InMemoryUsersAccountsStorageModule,
    UsersListComponent,
    UsersAccountsStateModule,
    UserAccountDetailsComponent,
  ],
  declarations: [UsersPage],
  providers: [],
  exports: [],
})
export class UsersPageModule {}
