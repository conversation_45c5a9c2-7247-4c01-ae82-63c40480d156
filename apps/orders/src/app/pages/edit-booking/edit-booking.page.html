<div class="top">
  <lib-developer-mode />
  <lib-issue-topbar />
</div>
<div class="container">
  <div class="content">
    <lib-not-available />
    <lib-general-package-details />
    @if (isEditing('packagePassengers')) {
      <lib-edit-package-passengers />
    }
    @if (isEditing('contact')) {
      <lib-edit-contact-details />
    }
    @if (isEditing('invoice')) {
      <lib-edit-invoice-details />
    }
    @if (isEditing('buyouts')) {
      <lib-edit-booking-buyout />
    }
    @if (isEditing('offer-prices')) {
      <lib-edit-offer-prices />
    }
    @if (isEditing('package-issuing')) {
      <lib-edit-package-issuing />
    }
    @if (isEditing('order-services')) {
      <lib-edit-order-services />
    }
    @if (isEditing('payments')) {
      <lib-edit-payments />
    }
    @if (isEditing('price-elements')) {
      <lib-edit-booking-price />
    }
    @if (productType === 'flight') {
      @switch (productSectionType) {
        @case ('baggage') {
          <lib-edit-flight-baggage />
        }
        @case ('general') {
          <lib-edit-flight-booking-general />
        }
        @case ('itinerary') {
          <lib-edit-flight-booking-itinerary />
        }
        @case ('passengers') {
          <lib-edit-flight-booking-passengers />
        }
        @case ('seats') {
          <lib-edit-flight-booking-seats />
        }
        @case ('tickets') {
          <lib-edit-flight-booking-tickets />
        }
      }
    }
    @if (productType === 'hotel') {
      @switch (productSectionType) {
        @case ('general') {
          <lib-edit-hotel-booking-general-info />
        }
      }
    }
    @if (productType === 'ancillary') {
      @switch (productSectionType) {
        @case ('general') {
          <lib-edit-ancillary-booking-general />
        }
      }
    }
    @if (productSectionType === 'cancellation') {
      <lib-booking-cancellation />
    }
    @if (productSectionType === 'issue') {
      <lib-issue-booking />
    }
  </div>
</div>
