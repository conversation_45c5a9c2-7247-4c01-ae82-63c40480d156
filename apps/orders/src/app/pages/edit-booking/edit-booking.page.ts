import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  templateUrl: 'edit-booking.page.html',
  styleUrls: ['edit-booking.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditBookingPage {
  productType = '';
  productSectionType = '';

  constructor(private readonly route: ActivatedRoute) {
    this.productType = this.route.snapshot.data['productType'] ?? '';
    this.productSectionType = this.route.snapshot.params.productSectionType ?? '';
  }

  isEditing(editType: string): boolean {
    return (this.route.snapshot.data['editType'] ?? '') === editType;
  }
}
