import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { EditBookingPage } from './edit-booking.page';

describe('EditBookingPage', () => {
  const given = async (data: Partial<ActivatedRoute>) => {
    await TestBed.configureTestingModule({
      declarations: [EditBookingPage],
      providers: [{ provide: ActivatedRoute, useValue: data }],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    const fixture = TestBed.createComponent(EditBookingPage);
    const component = fixture.componentInstance;

    return { component };
  };

  describe('Initialization', () => {
    [
      {
        whenData: {
          activatedRouteStub: {
            snapshot: {
              data: { wrongName: '__PRODUCT_TYPE__' },
              params: { wrongName: '__PRODUCT_SECTION_TYPE' },
            },
          },
        },
        thenData: {
          productType: '',
          productSectionType: '',
        },
      },
      {
        whenData: {
          activatedRouteStub: {
            snapshot: {
              data: { productType: '__PRODUCT_TYPE__' },
              params: { productSectionType: '__PRODUCT_SECTION_TYPE__' },
            },
          },
        },
        thenData: {
          productType: '__PRODUCT_TYPE__',
          productSectionType: '__PRODUCT_SECTION_TYPE__',
        },
      },
    ].forEach(({ whenData, thenData }, i) =>
      it('should initialize parameters from route data', async () => {
        const { component } = await given(whenData.activatedRouteStub as unknown as ActivatedRoute);

        expect(component.productType).toBe(thenData.productType);
        expect(component.productSectionType).toBe(thenData.productSectionType);
      }),
    );
  });

  describe('isEditing', () => {
    [
      {
        whenData: {
          activatedRouteStub: {
            snapshot: {
              data: { wrongProductType: '', wrongEditType: '' },
              params: { wrongSectionType: '' },
            },
          },
        },
        thenData: {
          isEditing: false,
        },
      },
      {
        whenData: {
          activatedRouteStub: {
            snapshot: {
              data: { productType: '__PRODUCT_TYPE__', editType: '__EDIT_TYPE__' },
              params: { productSectionType: '__PRODUCT_SECTION_TYPE__' },
            },
          },
        },
        thenData: {
          isEditing: true,
        },
      },
    ].forEach(({ whenData, thenData }, i) =>
      it('should return true if editType matches route data', async () => {
        const { component } = await given(whenData.activatedRouteStub as unknown as ActivatedRoute);

        expect(component.isEditing('__EDIT_TYPE__')).toBe(thenData.isEditing);
      }),
    );
  });
});
