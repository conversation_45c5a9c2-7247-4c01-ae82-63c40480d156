import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DeveloperModeComponent } from '@developer-mode';
import {
  BookingCancellationComponent,
  EditAncillaryBookingGeneralInfoComponent,
  EditAncillaryBookingStateModule,
  EditBookingBuyoutsComponent,
  EditBookingPriceComponent,
  EditContactDetailsComponent,
  EditFlightBaggageComponent,
  EditFlightBookingGeneralInfoComponent,
  EditFlightBookingItineraryComponent,
  EditFlightBookingPassengersComponent,
  EditFlightBookingSeatsComponent,
  EditFlightBookingTicketsComponent,
  EditHotelBookingGeneralInfoComponent,
  EditInsuranceBookingStateModule,
  EditInvoiceDetailsComponent,
  EditOfferPricesComponent,
  EditOrderServicesComponent,
  EditOrderServicesStateModule,
  EditPackageIssuingComponent,
  EditPackagePassengersComponent,
  EditPackageStateModule,
  EditPaymentsComponent,
  GraphqlEditAncillaryBookingServiceModule,
  GraphqlEditBookingBuyoutsServiceModule,
  GraphqlEditBookingPricesServiceModule,
  GraphqlEditContactDetailsServiceModule,
  GraphqlEditFlightBookingServiceModule,
  GraphqlEditHotelBookingServiceModule,
  GraphqlEditInsuranceBookingServiceModule,
  GraphqlEditInvoiceDetailsServiceModule,
  GraphqlEditOfferPricesServiceModule,
  GraphqlEditOrderServicesServiceModule,
  GraphqlEditPackageIssuingServiceModule,
  GraphqlEditPassengersServiceModule,
  GraphqlEditPaymentsServiceModule,
  InMemoryEditPackageStorageModule,
  IssueBookingComponent,
} from '@edit-package';
import {
  GeneralPackageDetailsComponent,
  GeneralPackageDetailsResolverModule,
  GeneralPackageDetailsStateModule,
  GraphqlGeneralPackageDetailsServiceModule,
  InMemoryGeneralPackageDetailsStorageModule,
} from '@general-package-details';
import {
  InMemoryIssuesStorageModule,
  IssuesStateModule,
  IssueTopbarComponent,
  PackageAvailableLoadedEventHandlerModule,
} from '@issues';
import {
  GraphqlPackageAvailabilityServiceModule,
  InMemoryPackageAvailabilityStorageModule,
  NotAvailableComponent,
  PackageAvailabilityEventsModule,
  PackageAvailabilityResolverModule,
  PackageAvailabilityStateModule,
} from '@package-availability';
import { TableModule } from 'primeng/table';
import { Tag } from 'primeng/tag';
import { EditBookingPage } from './edit-booking.page';

@NgModule({
  declarations: [EditBookingPage],
  exports: [],
  imports: [
    CommonModule,
    BookingCancellationComponent,
    DeveloperModeComponent,
    EditAncillaryBookingGeneralInfoComponent,
    EditInsuranceBookingStateModule,
    EditAncillaryBookingStateModule,
    EditContactDetailsComponent,
    EditBookingBuyoutsComponent,
    EditBookingPriceComponent,
    EditFlightBaggageComponent,
    EditFlightBookingGeneralInfoComponent,
    EditFlightBookingItineraryComponent,
    EditFlightBookingPassengersComponent,
    EditFlightBookingSeatsComponent,
    EditFlightBookingTicketsComponent,
    EditHotelBookingGeneralInfoComponent,
    EditInvoiceDetailsComponent,
    EditOfferPricesComponent,
    EditPackageIssuingComponent,
    EditOrderServicesComponent,
    EditPackagePassengersComponent,
    EditPackageStateModule,
    EditOrderServicesStateModule,
    EditPaymentsComponent,
    GraphqlEditAncillaryBookingServiceModule,
    GraphqlEditBookingBuyoutsServiceModule,
    GraphqlEditBookingPricesServiceModule,
    GraphqlEditContactDetailsServiceModule,
    GraphqlEditFlightBookingServiceModule,
    GraphqlEditHotelBookingServiceModule,
    GraphqlEditInsuranceBookingServiceModule,
    GraphqlEditInvoiceDetailsServiceModule,
    GraphqlEditOfferPricesServiceModule,
    GraphqlEditPackageIssuingServiceModule,
    GraphqlEditOrderServicesServiceModule,
    GraphqlEditPassengersServiceModule,
    GraphqlEditPaymentsServiceModule,
    GraphqlGeneralPackageDetailsServiceModule,
    GraphqlPackageAvailabilityServiceModule,
    GeneralPackageDetailsComponent,
    GeneralPackageDetailsResolverModule,
    GeneralPackageDetailsStateModule,
    InMemoryEditPackageStorageModule,
    InMemoryGeneralPackageDetailsStorageModule,
    InMemoryIssuesStorageModule,
    InMemoryPackageAvailabilityStorageModule,
    IssueBookingComponent,
    IssueTopbarComponent,
    IssuesStateModule,
    NotAvailableComponent,
    PackageAvailabilityEventsModule,
    PackageAvailabilityResolverModule,
    PackageAvailabilityStateModule,
    PackageAvailableLoadedEventHandlerModule,
    TableModule,
    Tag,
    EditOrderServicesComponent,
  ],
  providers: [],
})
export class EditBookingPageModule {}
