import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  GeneralPackageDetailsComponent,
  GeneralPackageDetailsResolverModule,
  GeneralPackageDetailsStateModule,
  GraphqlGeneralPackageDetailsServiceModule,
  InMemoryGeneralPackageDetailsStorageModule,
} from '@general-package-details';
import {
  InMemoryIssuesStorageModule,
  IssuesStateModule,
  IssueTopbarComponent,
  PackageAvailableLoadedEventHandlerModule,
} from '@issues';
import {
  GraphqlNotificationsServiceModule,
  InMemoryNotificationsStorageModule,
  NotificationsListComponent,
  NotificationsResolverModule,
  NotificationsStateModule,
} from '@notifications';
import {
  GraphqlPackageAvailabilityServiceModule,
  InMemoryPackageAvailabilityStorageModule,
  NotAvailableComponent,
  PackageAvailabilityEventsModule,
  PackageAvailabilityResolverModule,
  PackageAvailabilityStateModule,
} from '@package-availability';
import { NotificationsPage } from './notifications.page';

@NgModule({
  imports: [
    CommonModule,
    GeneralPackageDetailsComponent,
    GeneralPackageDetailsResolverModule,
    GeneralPackageDetailsStateModule,
    NotAvailableComponent,
    PackageAvailabilityResolverModule,
    PackageAvailabilityStateModule,
    InMemoryGeneralPackageDetailsStorageModule,
    InMemoryPackageAvailabilityStorageModule,
    GraphqlGeneralPackageDetailsServiceModule,
    GraphqlPackageAvailabilityServiceModule,
    GraphqlNotificationsServiceModule,
    NotificationsListComponent,
    NotificationsStateModule,
    NotificationsResolverModule,
    InMemoryNotificationsStorageModule,
    IssueTopbarComponent,
    PackageAvailabilityEventsModule,
    PackageAvailableLoadedEventHandlerModule,
    IssuesStateModule,
    InMemoryIssuesStorageModule,
    GeneralPackageDetailsStateModule,
  ],
  declarations: [NotificationsPage],
  providers: [],
  exports: [],
})
export class NotificationsPageModule {}
