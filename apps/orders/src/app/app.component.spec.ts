import { TestBed } from '@angular/core/testing';
import { Router, NavigationStart } from '@angular/router';
import { ReplaySubject } from 'rxjs';
import { take } from 'rxjs/operators';
import { AppComponent } from './app.component';

describe('AppComponent', () => {
  let routerEvents$: ReplaySubject<any>;
  const mockRouter = {
    events: new ReplaySubject<Event>(),
  };

  const given = () => {
    routerEvents$ = mockRouter.events as ReplaySubject<Event>;

    TestBed.configureTestingModule({
      declarations: [AppComponent],
      providers: [{ provide: Router, useValue: mockRouter }],
    }).compileComponents();

    const fixture = TestBed.createComponent(AppComponent);
    const component = fixture.componentInstance;

    return {
      component,
      fixture,
    };
  };

  it('should initialize component', () => {
    const { component } = given();
    expect(component.loading$).toBeDefined();
  });

  it('should emit true when NavigationStart event occurs', (done) => {
    const { component } = given();

    component.loading$.pipe(take(1)).subscribe((loading) => {
      expect(loading).toBe(true);
      done();
    });

    routerEvents$.next(new NavigationStart(1, '/'));
  });
});
