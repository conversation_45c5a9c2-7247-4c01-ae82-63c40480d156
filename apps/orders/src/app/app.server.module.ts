import { NgModule } from '@angular/core';
import { ServerModule } from '@angular/platform-server';
import { AppComponent } from './app.component';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { UiColorSpinnerComponentModule } from '@shared/ui';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    RouterModule.forRoot([{ path: '**', redirectTo: '', pathMatch: 'full' }]),
    ServerModule,
    UiColorSpinnerComponentModule,
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppServerModule {}
