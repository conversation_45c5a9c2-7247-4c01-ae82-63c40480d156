{"extends": "../../tsconfig.base.json", "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./tsconfig.editor.json"}], "compilerOptions": {"target": "es2020", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}