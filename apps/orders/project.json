{"name": "orders", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/orders/src", "prefix": "esky-pps-nx", "tags": ["scope:orders-app"], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/orders/browser", "index": "apps/orders/src/index.html.twig", "main": "apps/orders/src/main.ts", "polyfills": "apps/orders/src/polyfills.ts", "tsConfig": "apps/orders/tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["libs/shared/ui/src/assets/styles"]}, "assets": ["apps/orders/src/favicon.ico", "apps/orders/src/assets"], "styles": ["apps/orders/src/styles.scss", "node_modules/@ecs/design/esky-light/css/_variables.css", "node_modules/@pinta/style/base/css/_variables.css", "node_modules/prismjs/plugins/line-highlight/prism-line-highlight.css"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-css.min.js", "node_modules/prismjs/plugins/line-numbers/prism-line-numbers.js", "node_modules/prismjs/plugins/line-highlight/prism-line-highlight.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "apps/orders/src/environments/environment.ts", "with": "apps/orders/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "server": {"executor": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/orders/server", "main": "apps/orders/server.ts", "tsConfig": "apps/orders/tsconfig.server.json"}, "configurations": {"production": {"outputHashing": "media", "fileReplacements": [{"replace": "apps/orders/src/environments/environment.ts", "with": "apps/orders/src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false, "buildOptimizer": false}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "apps/orders/proxy.conf.ts"}, "configurations": {"production": {"buildTarget": "orders:build:production", "serverTarget": "orders:server:production"}, "development": {"buildTarget": "orders:build:development", "serverTarget": "orders:server:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "orders:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/orders"], "options": {"jestConfig": "apps/orders/jest.config.ts"}}, "serve-ssr": {"executor": "@angular-devkit/build-angular:ssr-dev-server", "options": {"proxyConfig": "apps/orders/proxy.conf.ts"}, "configurations": {"development": {"browserTarget": "orders:build:development", "serverTarget": "orders:server:development"}, "production": {"browserTarget": "orders:build:production", "serverTarget": "orders:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"executor": "@angular-devkit/build-angular:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "orders:build:production", "serverTarget": "orders:server:production"}, "development": {"browserTarget": "orders:build:development", "serverTarget": "orders:server:development"}}, "defaultConfiguration": "production"}}}