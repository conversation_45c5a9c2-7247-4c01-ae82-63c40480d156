import 'zone.js';
import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine } from '@angular/ssr';
import * as dotenv from 'dotenv';
import * as express from 'express';
import { NextFunction } from 'express';
import { join } from 'path';
import { renderFile, RenderOptions } from 'twig';
import { AppServerModule } from './src/main.server';
import { environment } from './src/environments/environment';

dotenv.config();

const commonEngine = new CommonEngine();

const getVariable: (name: string) => string | null = (name: string): string | null => {
  return process?.env[name] ?? null;
};

function getConfig(): Record<string, string | null> {
  return {
    authUrl: getVariable('AUTH_URL'),
  };
}

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  const distFolder = join(process.cwd(), 'dist/orders/browser');
  const indexHtml = join(distFolder, 'index.html.twig');

  server.engine(
    'twig',
    (templatePath: string, options: RenderOptions, callback: (err?: Error | null, html?: string) => void): void => {
      renderFile(templatePath, { config: options.config }, (err: Error, html: string) => {
        if (err) {
          console.error(err);
        }

        try {
          const { req } = options;
          const { protocol, baseUrl, url } = req;
          const res = options.res ?? req.res;

          commonEngine
            .render({
              ...options,
              bootstrap: AppServerModule,
              documentFilePath: indexHtml,
              url: options.url ?? `${protocol}://${req.get('host') ?? ''}${baseUrl}${url}`,
              publicPath: distFolder,
              providers: options.providers ?? [],
              document: html,
            })
            .then((html) => res.send(html))
            .catch(callback);
        } catch (err) {
          callback(err as Error);
        }
      });
    },
  );

  server.post('/graphql', (req, res) => {
    res.status(200).send({ status: 'OK', data: {} });
  });

  server.set('view engine', 'twig');
  server.set('views', distFolder);

  server.get('/config', (req, res) => {
    res.json({
      appEnv: getVariable('APP_ENV'),
      appName: environment.appName,
      appVersion: environment.appVersion,
      apiUrl: getVariable('API_URL'),
      authUrl: getVariable('AUTH_URL'),
      baseUrl: getVariable('BASE_URL'),
      bmsUrl: getVariable('BMS_URL'),
      notificationsUrl: getVariable('NOTIFICATIONS_URL'),
      operationsBaseUrl: getVariable('OPERATIONS_BASE_URL'),
      pmsHost: getVariable('PMS_HOST'),
    });
  });

  server.get('/api/**', (req, res) => {
    res.sendStatus(200);
  });

  // Serve static files from /browser
  server.get('*.*', express.static(distFolder, { maxAge: '1y' }));

  server.get('*', (req, res, next: NextFunction) => {
    try {
      const config: Record<string, string | null> = getConfig();

      res.render(indexHtml, {
        req,
        res,
        config,
        providers: [{ provide: APP_BASE_HREF, useValue: req.baseUrl }],
      });
    } catch (error) {
      console.error(error);
      return next(error);
    }
  });

  return server;
}

function run(): void {
  const port = process.env['PORT'] ?? 3000;

  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

// Webpack will replace 'require' with '__webpack_require__'
// '__non_webpack_require__' is a proxy to Node 'require'
// The below code is to ensure that the server is run only when not requiring the bundle.
declare const __non_webpack_require__: NodeRequire;

let mainModule;

if (typeof __non_webpack_require__ !== 'undefined') {
  mainModule = __non_webpack_require__.main;
} else if (process.env.NODE_ENV !== 'test') {
  mainModule = require.main;
}

const moduleFilename = mainModule?.filename ?? '';

if (moduleFilename === __filename || moduleFilename.includes('iisnode')) {
  run();
}

export * from './src/main.server';
