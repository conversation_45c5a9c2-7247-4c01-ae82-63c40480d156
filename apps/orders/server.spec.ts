import * as dotenv from 'dotenv';
import * as supertest from 'supertest';
import { app } from './server';

dotenv.config();
const request = supertest(app());

describe('Server', () => {
  describe('GET /config', () => {
    it('should return application configuration in JSON format', async () => {
      const response = await request.get('/config');
      expect(response.status).toBe(200);
      expect(response.body).toStrictEqual({
        appEnv: process.env.APP_ENV,
        appName: '__APP_NAME__',
        appVersion: '__APP_VERSION__',
        apiUrl: process.env.API_URL,
        authUrl: process.env.AUTH_URL,
        baseUrl: process.env.BASE_URL,
        bmsUrl: process.env.BMS_URL,
        notificationsUrl: process.env.NOTIFICATIONS_URL,
        operationsBaseUrl: process.env.OPERATIONS_BASE_URL,
        pmsHost: process.env.PMS_HOST,
      });
    });
  });

  describe('POST /graphql', () => {
    it('should respond with status 200 for POST requests to /graphql', async () => {
      const response = await request.post('/graphql');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ status: 'OK', data: {} });
    });
  });

  describe('GET /api/**', () => {
    it('should respond status 200 for GET /api/**', async () => {
      const response = await request.get('/api/v1');
      expect(response.status).toBe(200);
    });
  });
});
