name: FEATURE

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        required: true
        description: Where to deploy?
        options:
          - devdbr1
          - devdbr2
          - devdbr3

env:
  REGISTRY: eu.gcr.io
  API_IMAGE_NAME: esky.esky-pps-nx
  IMAGE_VENDOR: esky-dbr-microservices-pro

jobs:
  build_feature:
    runs-on: k8s-runner
    steps:
      - run: echo ${{github.run_number}}
      - name: Login to docker
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}
      - name: Login to europe-docker.pgk.dev
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry: europe-docker.pkg.dev
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Build docker image
        run: docker build . -t ${{env.REGISTRY}}/${{env.IMAGE_VENDOR}}/${{env.API_IMAGE_NAME}}:latest --build-arg APP_NAME=${{env.API_IMAGE_NAME}} --build-arg APP_VERSION=b${{github.run_number}}-spin-${{ inputs.environment }}
      - name: Publish docker image
        uses: eskygroup/github-actions/.github/actions/docker-push@master
        with:
          imageVendor: ${{env.IMAGE_VENDOR}}
          appList: ${{env.API_IMAGE_NAME}}
          tagSuffix: spin-${{ inputs.environment }}
