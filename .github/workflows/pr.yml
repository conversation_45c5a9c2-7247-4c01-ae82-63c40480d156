name: PR

on:
  pull_request:
    branches: ['master']

env:
  REGISTRY: eu.gcr.io
  API_IMAGE_NAME: esky.esky-pps-nx
  IMAGE_VENDOR: esky-dbr-microservices-pro

jobs:
  build_pr:
    runs-on: k8s-runner
    steps:
      - name: Login to docker
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}
      - name: Login to europe-docker.pgk.dev
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry: europe-docker.pkg.dev
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Show workdir
        run: ls
      - name: Docker up
        run: docker-compose up -d
      - name: Install
        run: docker-compose exec -T cli bash -c "npm i --include=dev --force --loglevel error"
      - name: Run tests and linter
        run: docker-compose exec -T cli bash -c "npm run lint:all && npm run test:all"
      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      - name: SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      - name: Docker down
        if: always()
        run: docker-compose down --remove-orphans
