import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import {
  BuyoutDetailsComponent,
  UiBackdropButtonComponent,
  UiBadgeComponent,
  UiSidebarComponentModule,
  UiStatusComponent,
} from '@shared/ui';
import { BaseBookingDirective } from '../../directives/base-booking/base-booking.directive';
import { BookingPriceComponent } from '../booking-elements/booking-price/booking-price.component';
import { BuyoutComponent } from '../booking-elements/buyout/buyout.component';
import { PersonComponent } from '../booking-elements/person/person.component';
import { IssuingAttemptsComponent } from '../issuing-attempts/issuing-attempts.component';
import { InsuranceBookingsComponent } from './insurance-bookings.component';

@NgModule({
  imports: [
    BaseBookingDirective,
    BookingPriceComponent,
    BuyoutComponent,
    BuyoutDetailsComponent,
    CommonModule,
    CoreAngularModule,
    PersonComponent,
    PmsImagePipeModule,
    RouterLink,
    UiStatusComponent,
    UiBackdropButtonComponent,
    UiSidebarComponentModule,
    IssuingAttemptsComponent,
    UiBadgeComponent,
  ],
  declarations: [InsuranceBookingsComponent],
  providers: [],
  exports: [InsuranceBookingsComponent],
})
export class InsuranceBookingsComponentModule {}
