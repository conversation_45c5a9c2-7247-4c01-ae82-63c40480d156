import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { Observable } from 'rxjs';
import {
  GETS_INSURANCE_BOOKINGS_QUERY,
  GetsInsuranceBookingsQueryPort,
} from '../../../../application/ports/primary/query/gets-insurance-bookings.query-port';
import { InsuranceBookingsQuery } from '../../../../application/ports/primary/query/insurance-bookings.query';

@Component({
  selector: 'lib-insurance-bookings',
  templateUrl: 'insurance-bookings.component.html',
  styleUrls: ['insurance-bookings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InsuranceBookingsComponent {
  readonly insuranceBookings$: Observable<InsuranceBookingsQuery> =
    this.getInsuranceBookingsQueryPort.getInsuranceBookingsQuery();

  constructor(
    @Inject(GETS_INSURANCE_BOOKINGS_QUERY)
    private readonly getInsuranceBookingsQueryPort: GetsInsuranceBookingsQueryPort,
  ) {}
}
