@if (insuranceBookings$ | async; as insuranceBookings) {
  @for (insuranceBooking of insuranceBookings.insuranceBookings; let i = $index; track i) {
    <div baseBooking #baseBooking="baseBooking" class="rounded-container" [id]="'insuranceBookingIndex=' + i">
      @if (!insuranceBooking.isVisible) {
        <div class="booking-visibility-information">
          <ecs-icon class="icon-xl mr-m status alert" name="eye_crossed" />
          <div class="info">
            <p class="title">Hidden booking</p>
            <p class="description">This booking is not visible for the end user</p>
          </div>
        </div>
      }
      <div class="section-title-container justify-content-sb">
        <div class="title-container" [class.edit-booking]="insuranceBooking.canEdit">
          <p class="title">Insurance</p>
          <p class="subtitle mr-l">{{ insuranceBooking.productId }}</p>
          <ui-status [status]="insuranceBooking.bookingStatus" />
          @if (insuranceBooking.tags) {
            @for (tag of insuranceBooking.tags; track $index) {
              <ui-badge class="ml-l" [text]="tag" />
            }
          }
        </div>
        <div class="d-flex">
          @if (insuranceBooking.canEdit) {
            <ecs-button kind="tertiary ml-xl" icon="pencil" iconPosition="right" (click)="editOptionsDialog.open()">
              Edit
            </ecs-button>
            <ui-backdrop-button class="ml-xs" size="medium">
              <a
                data-selector="edit-issue-redirect-anchor"
                class="option"
                (click)="
                  baseBooking.openEditPage(
                    insuranceBooking.canIssue,
                    insuranceBooking.linkQuery.getEditRouterLink('issue')
                  )
                "
                [class.disabled]="!insuranceBooking.canIssue"
                #link
                target="_blank"
              >
                <ecs-icon class="option-icon" [ngClass]="{ disabled: !insuranceBooking.canIssue }" name="check_line" />
                Issuing
              </a>

              <a
                data-selector="edit-booking-cancellation-anchor"
                class="option"
                [class.disabled]="!insuranceBooking.canCancel"
                (click)="
                  baseBooking.openEditPage(
                    insuranceBooking.canCancel,
                    insuranceBooking.linkQuery.getEditRouterLink('cancellation')
                  )
                "
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !insuranceBooking.canCancel }"
                  name="action_trashcan"
                />
                Cancel
              </a>

              <a
                data-selector="edit-booking-hide-anchor"
                class="option"
                [class.disabled]="!insuranceBooking.canChangeVisibility"
                (click)="
                  baseBooking.onChangeBookingVisibility(
                    insuranceBooking.canChangeVisibility,
                    insuranceBooking.isVisible,
                    insuranceBooking.productId,
                    insuranceBooking.itemId
                  )
                "
                #link
                target="_blank"
              >
                @if (insuranceBooking.isVisible) {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !insuranceBooking.canChangeVisibility }"
                    name="eye_line_crossed"
                  />
                  Hide
                } @else {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !insuranceBooking.canChangeVisibility }"
                    name="eye_line"
                  />
                  Show
                }
              </a>
            </ui-backdrop-button>
          }
        </div>
      </div>
      <div class="booking">
        <div class="booking-main-section-container">
          <div class="data-container mb-xl">
            <div class="data">
              <p class="label">Provider ID</p>
              <p class="value">{{ insuranceBooking.providerBookingId }}</p>
            </div>
            <div class="data">
              <p class="label">Provider</p>
              <p class="value">{{ insuranceBooking.providerName }}</p>
            </div>
            <div class="data">
              <p class="label">Product ID</p>
              <p class="value">{{ insuranceBooking.productId }}</p>
            </div>
            <div class="data">
              <p class="label">Original provider</p>
              <p class="value">{{ insuranceBooking.originalProviderName }}</p>
            </div>
            <div
              #providerStatusAnchorInDetails
              data-selector="provider-status-anchor-in-details"
              [ngClass]="['data', !!insuranceBooking.providerAdditionalMessage ? 'cursor-pointer' : '']"
              (click)="
                !!insuranceBooking.providerAdditionalMessage &&
                  baseBooking.onProviderStatusPopoverOpen(providerStatusAnchorInDetails)
              "
            >
              <p class="label">Provider status</p>
              <p class="value">{{ insuranceBooking.providerStatus }}</p>
            </div>
          </div>
          <div class="booking-main-data-container">
            <div class="booking-main-data-card">
              <div class="content">
                <div class="d-flex justify-content-sb booking-data-card-spacer">
                  <div class="booking-data-card-dates-container">
                    <p class="booking-data-card-date">{{ insuranceBooking.startDate | date: 'dd.MM.yyyy' }}</p>
                    <img class="date-icon" [src]="'icon_circle_with_arrow_right' | pmsImage" alt="arrow" />
                    <p class="booking-data-card-date">{{ insuranceBooking.endDate | date: 'dd.MM.yyyy' }}</p>
                  </div>
                  @if (insuranceBooking?.travelServicePrice?.price) {
                    <p class="booking-data-card-additional-info">
                      Travel service price: <span class="value">{{ insuranceBooking.travelServicePrice?.price }}</span>
                    </p>
                  }
                </div>
                <p class="booking-data-card-title">{{ insuranceBooking.name }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="booking-sub-section-container">
          @for (person of insuranceBooking.insuredPersons; track $index) {
            <div class="booking-sub-section-card">
              <lib-person [person]="person" />
            </div>
          }
        </div>

        @if (insuranceBooking.bookingPrice) {
          <div class="buyouts">
            <lib-booking-price
              [bookingPrice]="insuranceBooking.bookingPrice"
              totalPriceLabel="Insurance Price"
            ></lib-booking-price>

            <ecs-link
              class="d-block mt-m"
              size="small"
              data-selector="insurance-bookings-price-preview-anchor"
              target="_blank"
              [href]="insuranceBooking.linkQuery.getPreviewRouterLink('price-elements')"
            >
              Provider price preview
            </ecs-link>
            @if (insuranceBooking.buyouts.length) {
              <div class="mt-xs">
                @for (buyout of insuranceBooking.buyouts; track $index) {
                  <lib-buyout
                    class="mt-m card-hover-shadow"
                    [buyout]="buyout"
                    [index]="$index"
                    (click)="sidebar.openSidebar()"
                  />
                  <ui-sidebar sidebarTitle="Buyout details" #sidebar>
                    <ui-buyout-details [buyout]="buyout" />
                  </ui-sidebar>
                }
              </div>
            }
          </div>
        }
      </div>

      <lib-issuing-attempts [issuingAttempts]="insuranceBooking.issuingAttempts" />
      <ecs-dialog
        #editOptionsDialog
        class="ecs-dialog-standard"
        [hasBackdrop]="true"
        data-selector="options-dialog"
        (ecsBackdropClick)="editOptionsDialog.close()"
        (ecsEscapeKeydown)="editOptionsDialog.close()"
      >
        <ecs-dialog-header
          class="ecs-header"
          slot="dialog-header"
          [headerTitle]="'Edit insurance - ' + insuranceBooking.productId"
          (ecsCloseButtonClick)="editOptionsDialog.close()"
        ></ecs-dialog-header>
        <div class="body" slot="dialog-body">
          <p class="text-2-color mb-xl">Choose which one of element you want to edit</p>
          <div class="d-flex">
            <div
              class="square-option mr-s"
              data-selector="edit-insurance-booking-price-anchor"
              [routerLink]="insuranceBooking.linkQuery.getEditRouterLink('price-elements')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="money_coins" />
              <p class="name">Price</p>
            </div>

            @if (!insuranceBookings.showBookingPriceBuyoutAssignment) {
              <div
                class="square-option"
                data-selector="edit-insurance-booking-buyouts-anchor"
                [routerLink]="insuranceBooking.linkQuery.getEditRouterLink('buyouts')"
                (click)="editOptionsDialog.close()"
              >
                <ecs-icon class="icon-xl mb-xs" name="buyouts" />
                <p class="name">Buyouts</p>
              </div>
            }
          </div>
        </div>
      </ecs-dialog>
      <div>
        <ecs-popover
          #providerStatusPopover
          data-selector="provider-status-popover"
          id="front-layer"
          [relativePosition]="'right-bottom'"
          [anchorElement]="baseBooking.providerStatusAnchor"
          positionId="provider-additional-message"
        >
          <ecs-popover-header
            data-selector="provider-status-popover-header"
            headerTitle="Provider message"
            slot="popover-header"
            (ecsCloseButtonClick)="baseBooking.onProviderStatusPopoverClose()"
          >
          </ecs-popover-header>
          <div class="popover-body" slot="popover-body" data-selector="provider-status-popover-body">
            {{ insuranceBooking.providerAdditionalMessage }}
          </div>
        </ecs-popover>
        <ecs-backdrop
          #providerStatusPopoverBackdrop
          class="popover-backdrop"
          (click)="baseBooking.onProviderStatusPopoverClose()"
        ></ecs-backdrop>
      </div>
    </div>
  }
}
