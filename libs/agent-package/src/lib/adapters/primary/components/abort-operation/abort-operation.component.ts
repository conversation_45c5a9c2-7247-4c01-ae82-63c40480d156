import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormControl, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CoreAngularModule, EcsDialog } from '@ecs/angular';

@Component({
  selector: 'lib-abort-operation',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, ReactiveFormsModule],
  templateUrl: 'abort-operation.component.html',
  styleUrls: ['abort-operation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AbortOperationComponent {
  @ViewChild('abortOperationDialog') abortOperationDialog!: EcsDialog;
  @Output() abortConfirmed: EventEmitter<string> = new EventEmitter<string>();

  readonly reasonFormControl: FormControl<string> = this.formBuilder.control('', [
    Validators.required,
    Validators.minLength(3),
  ]);
  isLoading = false;
  isAbortOperationInProgress = false;

  constructor(
    private readonly formBuilder: NonNullableFormBuilder,
    private readonly changeDetectorRef: ChangeDetectorRef,
  ) {}

  async open(): Promise<void> {
    await this.abortOperationDialog.open();
  }

  async close(): Promise<void> {
    if (!this.isLoading) {
      this.resetReason();
    }
    await this.abortOperationDialog.close();
  }

  onAbortConfirmed(): void {
    if (!this.reasonFormControl.valid) {
      return;
    }

    this.abortConfirmed.emit(this.reasonFormControl.value);
    this.isLoading = true;
  }

  setOperationInProgress(): void {
    this.isAbortOperationInProgress = true;
    this.isLoading = false;
    this.resetReason();
    this.changeDetectorRef.detectChanges();
  }

  private resetReason(): void {
    this.reasonFormControl.setValue('');
    this.reasonFormControl.markAsUntouched();
  }
}
