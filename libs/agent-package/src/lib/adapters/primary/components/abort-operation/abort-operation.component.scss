@import 'libs/shared/ui/src/assets/styles/components';
@import 'libs/shared/ui/src/assets/styles/dialog';

.abort-operation-header {
  margin: var(--spacing-xl) var(--spacing-xl) 0;
}

.abort-operation-body {
  width: 418px;
  font-size: var(--font-size-s);
  line-height: var(--line-height-m);

  .info {
    line-height: var(--line-height-m);
    font-size: var(--font-size-m);
    color: var(--text-1-color);
  }

  .title {
    margin-bottom: var(--spacing-xl);
    font-weight: var(--font-weight-bold);
  }
}

.abort-operation-footer {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.abort-operation-in-progress-body {
  margin: var(--spacing-xl) var(--spacing-xl) 0;
  width: 418px;

  .emoji {
    text-align: center;
    font-size: 56px;
    line-height: 58px;
    margin-bottom: var(--spacing-s);
  }

  .title {
    text-align: center;
    font-size: 34px;
    font-weight: var(--font-weight-bold);
    line-height: 34px;
    margin-bottom: var(--spacing-s);
  }

  .text {
    color: var(--text-2-color);
    text-align: center;
    font-size: var(--font-size-s);
    line-height: var(--line-height-m);
  }
}

.abort-operation-in-progress-footer {
  width: 100%;
  margin: var(--spacing-xxs) var(--spacing-xl) var(--spacing-xxl);
}

.dialog {
  --body-padding: var(--spacing-m) var(--spacing-xxl-4) var(--spacing-xl);
  --border-radius: var(--border-radius-m);
}
