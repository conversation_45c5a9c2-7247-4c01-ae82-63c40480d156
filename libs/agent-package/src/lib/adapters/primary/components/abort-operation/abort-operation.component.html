<ecs-dialog
  #abortOperationDialog
  class="ecs-dialog-standard"
  [hasBackdrop]="true"
  data-selector="abort-operation-dialog"
  (ecsBackdropClick)="close()"
  (ecsEscapeKeydown)="close()"
>
  <ng-container *ngIf="!isAbortOperationInProgress; else abortOperationInProgress">
    <ecs-dialog-header
      class="abort-operation-header"
      slot="dialog-header"
      headerTitle="Force import of this package to BMS"
      (ecsCloseButtonClick)="close()"
    ></ecs-dialog-header>
    <div class="abort-operation-body" slot="dialog-body" data-selector="abort-operation-body">
      <ecs-announcement class="mb-m" kind="alert" icon="exclamation_mark_full">
        <span slot="heading">Important</span>
        <p class="info" slot="description">
          This action will force the package to be imported into the legacy booking management system (BMS).
          Be sure to check the status of products and transactions.
          Make note: flight + hotel packages can be manually handled in PPS and normally should not be imported to BMS.
        </p>
      </ecs-announcement>
      <p class="title">Are you sure you want to continue?</p>
      <p>Reason<span class="red-star">*</span></p>
      <textarea
        class="textarea"
        [formControl]="reasonFormControl"
        placeholder="e.g. the need to export in order to buy a service, change data, change the price, etc."
        data-selector="abort-operation-reason-textarea"
      ></textarea>
      <p class="form-error-message" *ngIf="!reasonFormControl.valid && reasonFormControl.touched">
        <ng-container *ngIf="reasonFormControl.hasError('required')"> Reason is required </ng-container>
        <ng-container *ngIf="reasonFormControl.hasError('minlength')">
          The reason must be at least 3 letters long
        </ng-container>
      </p>
    </div>
    <div class="ecs-footer" slot="dialog-footer">
      <ecs-button
        class="w-100"
        outline="true"
        data-selector="abort-operation-cancel-button"
        [disabled]="isLoading"
        (click)="close()"
      >
        Cancel
      </ecs-button>
      <ecs-button
        class="w-100"
        data-selector="abort-operation-confirm-button"
        [disabled]="!reasonFormControl.valid || isLoading"
        (click)="onAbortConfirmed()"
      >
        Confirm
      </ecs-button>
    </div>
  </ng-container>
  <ng-template #abortOperationInProgress>
    <div class="abort-operation-in-progress-body" slot="dialog-body" data-selector="abort-operation-in-progress-body">
      <p class="emoji">🚀</p>
      <p class="title">Import has started</p>
      <p class="text">you should soon see the package in BMS</p>
    </div>
    <div class="abort-operation-in-progress-footer" slot="dialog-footer">
      <ecs-button
        class="w-100"
        outline="true"
        data-selector="abort-operation-in-progress-close-button"
        (click)="close()"
      >
        Close
      </ecs-button>
    </div>
  </ng-template>
</ecs-dialog>
