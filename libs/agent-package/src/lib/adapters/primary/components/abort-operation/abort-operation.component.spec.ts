import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AbortOperationComponent } from './abort-operation.component';
import { AbortOperationComponentHarness } from './abort-operation.component-harness';

describe('AbortOperationComponent', () => {
  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [AbortOperationComponent, ReactiveFormsModule],
      declarations: [TestPage],
    }).compileComponents();

    const fixture = TestBed.createComponent(TestPage);
    const loader = TestbedHarnessEnvironment.loader(fixture);
    const componentHarness = await loader.getHarness(AbortOperationComponentHarness);
    const testComponent = fixture.componentInstance;
    const abortOperationComponent: AbortOperationComponent = fixture.debugElement.query(
      By.directive(AbortOperationComponent),
    ).componentInstance;

    return {
      componentHarness,
      testPageOnAbortOperationSpy: jest.spyOn(testComponent, 'onAbortOperation'),
      abortOperationComponent,
    };
  };

  [
    {
      whenData: '__REASON__',
      thenData: {
        reason: '__REASON__',
        isLoading: true,
      },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should abort process with reason after confirmation #${i + 1}`, async () => {
      const { componentHarness, testPageOnAbortOperationSpy, abortOperationComponent } = await given();

      await componentHarness.fillInput('abort-operation-reason-textarea', whenData);
      await componentHarness.clickOnElement('abort-operation-confirm-button');

      expect(testPageOnAbortOperationSpy).toHaveBeenCalledWith(thenData.reason);
      expect(abortOperationComponent.isLoading).toEqual(thenData.isLoading);
    }),
  );

  [
    {
      whenData: '',
    },
    {
      whenData: 'a',
    },
  ].forEach(({ whenData }, i) =>
    it(`should not abort process when reason is invalid #${i + 1}`, async () => {
      const { componentHarness, testPageOnAbortOperationSpy } = await given();

      await componentHarness.fillInput('abort-operation-reason-textarea', whenData);
      await componentHarness.clickOnElement('abort-operation-confirm-button');

      expect(testPageOnAbortOperationSpy).not.toHaveBeenCalled();
    }),
  );

  [
    {
      whenData: 'abc',
      thenData: {
        reason: '',
      },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should close confirmation section and reset reason on click cancel button #${i + 1}`, async () => {
      const { componentHarness, abortOperationComponent } = await given();

      await componentHarness.fillInput('abort-process-operation-textarea', whenData);
      await componentHarness.clickOnElement('abort-operation-cancel-button');
      const confirmationSection = await componentHarness.getElement('abort-operation-footer');

      expect(confirmationSection).toEqual(null);
      expect(abortOperationComponent.reasonFormControl.value).toEqual(thenData.reason);
    }),
  );

  it(`should render operation in progress body when abort process is in progress`, async () => {
    const { componentHarness, abortOperationComponent } = await given();

    abortOperationComponent.setOperationInProgress();

    const operationBody = await componentHarness.getElement('abort-operation-body');
    const operationInProgressBody = await componentHarness.getElement('abort-operation-in-progress-body');

    expect(operationBody).toEqual(null);
    expect(operationInProgressBody).toBeTruthy();
  });
});

@Component({
  template: '<lib-abort-operation (abortConfirmed)="onAbortOperation($event)" />',
})
class TestPage {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onAbortOperation(reason: string): void {}
}
