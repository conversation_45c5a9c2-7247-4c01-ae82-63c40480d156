import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import { EcsIconFilenamePipe, UiBadgeComponent } from '@shared/ui';
import { PersonQuery } from '../../../../../application/ports/primary/query/person.query';

@Component({
  selector: 'lib-person',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, EcsIconFilenamePipe, PmsImagePipeModule, UiBadgeComponent],
  templateUrl: 'person.component.html',
  styleUrls: ['person.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonComponent {
  @Input({ required: true }) person!: PersonQuery;
}
