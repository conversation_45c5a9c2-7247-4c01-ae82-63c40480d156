@import 'libs/shared/ui/src/assets/styles/booking-layout';

:host {
  min-width: 312px;
  height: fit-content;
}

.price-content {
  width: 100%;
  margin-bottom: auto;
  padding: var(--spacing-s);
  background-color: var(--color-gray-0);
  border-radius: var(--border-radius-xs);

  .price-item {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    font-size: var(--font-size-xs);

    &:not(:first-of-type) {
      margin-top: var(--spacing-xs);
    }

    .price-label {
      color: var(--text-2-color);
      margin-right: var(--spacing-xs);
      word-break: break-word;
    }

    .price-value {
      font-weight: var(--font-weight-medium);
      white-space: nowrap;
    }

    &.disabled {
      .price-label,
      .price-value {
        color: var(--color-gray-60);
        text-decoration: line-through;
      }
    }
  }
}

.total-price-content {
  padding: var(--spacing-s);
  border-radius: var(--border-radius-xs);
  width: 100%;
  margin-top: var(--spacing-m);
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  background-color: var(--color-primary-04);
  font-size: var(--font-size-s);

  .price-label {
    margin-right: var(--spacing-xs);
    word-break: break-word;
  }

  .price-value {
    font-weight: var(--font-weight-bold);
    white-space: nowrap;
  }
}
