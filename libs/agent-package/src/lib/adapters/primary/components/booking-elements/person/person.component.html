<ecs-icon class="icon-m mr-xs" [name]="person.personIcon | ecsIconFilename" />
<div class="person-content">
  <div class="d-flex align-items-center">
    <p class="person-label">{{ person.type }} - {{ person.birthDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>
    @if (person.isRemoved) {
      <ui-badge class="badge">Deleted</ui-badge>
    }
  </div>
  <p class="full-name">
    <ecs-tooltip class="tooltip" kind="dark" offset="12" [text]="person.nameInTooltip">
      {{ person.fullName }}
    </ecs-tooltip>
  </p>
</div>
