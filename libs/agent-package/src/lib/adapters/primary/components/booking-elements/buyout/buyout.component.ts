import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import { BuyoutQuery } from '../../../../../application/ports/primary/query/buyout.query';

@Component({
  selector: 'lib-buyout',
  standalone: true,
  imports: [CommonModule, PmsImagePipeModule, CoreAngularModule],
  templateUrl: 'buyout.component.html',
  styleUrl: 'buyout.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BuyoutComponent {
  @Input({ required: true }) buyout!: BuyoutQuery | null;
  @Input({ required: true }) index!: number;
}
