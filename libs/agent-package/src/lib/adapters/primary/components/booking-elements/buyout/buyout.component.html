@if (!!buyout) {
  <div class="buyout border-default" data-selector="buyout-container" [ngClass]="{ inactive: !buyout.isActive }">
    <div class="d-flex align-items-center" [ngClass]="{ inactive: !buyout.isActive }">
      <ecs-icon class="mr-xxs icon-xs" [name]="buyout.categoryIcon" />
      <p class="f-s-12">Buyout {{ index + 1 }}</p>
    </div>
    <div class="d-flex" [ngClass]="{ inactive: !buyout.isActive }">
      <ecs-icon class="mr-xxs icon-xs" [name]="buyout.methodIcon" />
      <p class="f-s-12">{{ buyout.type }}</p>
    </div>
  </div>
}
