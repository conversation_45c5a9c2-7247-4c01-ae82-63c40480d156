<div class="price-content">
  <div
    *ngFor="let priceItem of bookingPrice.priceItems"
    class="price-item"
    [ngClass]="['price-item', priceItem?.isDisabled ? 'disabled' : '']"
  >
    <p class="price-label">{{ priceItem?.name }}</p>
    <p class="price-value">{{ priceItem?.price?.price }}</p>
  </div>
</div>
<div class="total-price-content">
  <p class="price-label">{{ totalPriceLabel }}</p>
  <p class="price-value">{{ bookingPrice.total?.price }}</p>
</div>
