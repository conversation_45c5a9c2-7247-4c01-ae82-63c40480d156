import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BookingPriceQuery } from '../../../../../application/ports/primary/query/booking-price.query';

@Component({
  selector: 'lib-booking-price',
  standalone: true,
  imports: [CommonModule],
  templateUrl: 'booking-price.component.html',
  styleUrls: ['booking-price.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BookingPriceComponent {
  @Input({ required: true }) bookingPrice!: BookingPriceQuery;
  @Input({ required: true }) totalPriceLabel!: string;
}
