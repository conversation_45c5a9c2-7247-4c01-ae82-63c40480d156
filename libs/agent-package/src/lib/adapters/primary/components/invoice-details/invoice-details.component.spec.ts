import { TestBed } from '@angular/core/testing';
import { first, of } from 'rxjs';
import { InvoiceDetailsComponent } from './invoice-details.component';
import {
  GETS_INVOICE_DETAILS_QUERY,
  GetsInvoiceDetailsQueryPort,
} from '../../../../application/ports/primary/query/gets-invoice-details.query-port';
import { InvoiceDetailsQuery } from '../../../../application/ports/primary/query/invoice-details.query';
import { InvoiceContactDetailsQuery } from '../../../../application/ports/primary/query/invoice-contact-details.query';
import { InvoicePayerDetailsQuery } from '../../../../application/ports/primary/query/invoice-payer-details.query';

describe('InvoiceDetailsComponent', () => {
  const INVOICE_DETAILS_QUERY_STUB = new InvoiceDetailsQuery(
    true,
    '__PACKAGE_NUMBER__',
    new InvoiceContactDetailsQuery('__FIRST_NAME__', '__LAST_NAME__', '__EMAIL__', '__PHONE_NUMBER__'),
    new InvoicePayerDetailsQuery(
      true,
      false,
      true,
      '__TAX_NUMBER__',
      '__SECONDARY_TAX_NUMBER__',
      '__COMPANY_NAME__',
      '__IDENTIFICATION_NUMBER__',
      '__FIRST_NAME__',
      '__LAST_NAME__',
      '__DATE_OF_BIRTH__',
      '__ADDRESS__',
    ),
  );

  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [InvoiceDetailsComponent],
      providers: [
        {
          provide: GETS_INVOICE_DETAILS_QUERY,
          useValue: {
            getInvoiceDetailsQuery: () => of(INVOICE_DETAILS_QUERY_STUB),
          } as GetsInvoiceDetailsQueryPort,
        },
      ],
    }).compileComponents();

    return {
      component: TestBed.createComponent(InvoiceDetailsComponent).componentInstance,
    };
  };

  it('should create component', async () => {
    const { component } = await given();

    component.invoiceDetails$.pipe(first()).subscribe((data) => {
      expect(data).toEqual(INVOICE_DETAILS_QUERY_STUB);
    });
  });
});
