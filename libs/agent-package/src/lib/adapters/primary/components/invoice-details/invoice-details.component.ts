import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { Observable } from 'rxjs';
import {
  GETS_INVOICE_DETAILS_QUERY,
  GetsInvoiceDetailsQueryPort,
} from '../../../../application/ports/primary/query/gets-invoice-details.query-port';
import { InvoiceDetailsQuery } from '../../../../application/ports/primary/query/invoice-details.query';

@Component({
  selector: 'lib-invoice-details',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, RouterModule],
  templateUrl: 'invoice-details.component.html',
  styleUrls: ['invoice-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InvoiceDetailsComponent {
  readonly invoiceDetails$: Observable<InvoiceDetailsQuery> = this.getsPayerDataQueryPort.getInvoiceDetailsQuery();

  constructor(@Inject(GETS_INVOICE_DETAILS_QUERY) private readonly getsPayerDataQueryPort: GetsInvoiceDetailsQueryPort) {}
}
