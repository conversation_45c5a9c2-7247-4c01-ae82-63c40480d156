import { TestBed } from '@angular/core/testing';
import { first, of } from 'rxjs';
import { ContactDetailsQuery } from '../../../../application/ports/primary/query/contact-details.query';
import {
  GETS_CONTACT_DETAILS_QUERY,
  GetsContactDetailsQueryPort,
} from '../../../../application/ports/primary/query/gets-contact-details.query-port';
import { UserAccountQuery } from '../../../../application/ports/primary/query/user-account.query';
import { ContactDetailsComponent } from './contact-details.component';

describe('ContactDetailsComponent', () => {
  const CONTACT_DETAILS_QUERY_STUB = new ContactDetailsQuery(
    '__PACKAGE_NUMBER__',
    '__FIRST_NAME__',
    '__LAST_NAME__',
    '__EMAIL__',
    '__PHONE_NUMBER__',
    true,
    new UserAccountQuery('__USER_EMAIL__', '__USER_ID__'),
    '__URL__',
  );

  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [ContactDetailsComponent],
      providers: [
        {
          provide: GETS_CONTACT_DETAILS_QUERY,
          useValue: {
            getContactDetailsQuery: () => of(CONTACT_DETAILS_QUERY_STUB),
          } as GetsContactDetailsQueryPort,
        },
      ],
    }).compileComponents();

    return {
      component: TestBed.createComponent(ContactDetailsComponent).componentInstance,
    };
  };

  it('should create component', async () => {
    const { component } = await given();

    component.contactDetails$.pipe(first()).subscribe((data) => {
      expect(data).toEqual(CONTACT_DETAILS_QUERY_STUB);
    });
  });
});
