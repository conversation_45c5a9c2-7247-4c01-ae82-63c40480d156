@if (contactDetails$ | async; as contactDetails) {
  <div class="rounded-container">
    <div class="section-title-container align-items-center">
      <div class="title-container">
        <p class="title">Contact details</p>
      </div>
      @if (contactDetails.canEdit) {
        <ecs-button
          class="action-button"
          iconPosition="right"
          kind="tertiary"
          size="small"
          icon="pencil"
          [routerLink]="[
            '/orders',
            contactDetails.packageNumber,
            'edit',
            'contact-details'
          ]"
          data-selector="contact-details-redirect-to-edit-form-button"
        >
          Edit
        </ecs-button>
      }
      @if (contactDetails.userAccount.userId) {
        <a
          class="ml-a"
          data-selector="contact-details-user-account-page-button"
          [href]="contactDetails.userAccountPageUrl"
          target="_blank"
        >
          <ecs-button kind="tertiary">User Zone account</ecs-button>
        </a>
      }
    </div>
    <div class="data-container">
      <div class="data">
        <p class="label">Name</p>
        <p class="value">{{ contactDetails.firstName }}</p>
      </div>
      <div class="data">
        <p class="label">Surname</p>
        <p class="value">{{ contactDetails.lastName }}</p>
      </div>
      <div class="data">
        <p class="label">Phone</p>
        <p class="value">{{ contactDetails.phoneNumber }}</p>
      </div>
      <div class="data">
        <p class="label">E-mail</p>
        <p class="value">{{ contactDetails.email }}</p>
      </div>
      <div class="data">
        <p class="label">User Zone e-mail</p>
        <p class="value">{{ contactDetails.userAccount.email }}</p>
      </div>
    </div>
  </div>
}
