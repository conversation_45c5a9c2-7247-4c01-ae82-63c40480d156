import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { Observable } from 'rxjs';
import { ContactDetailsQuery } from '../../../../application/ports/primary/query/contact-details.query';
import {
  GETS_CONTACT_DETAILS_QUERY,
  GetsContactDetailsQueryPort,
} from '../../../../application/ports/primary/query/gets-contact-details.query-port';

@Component({
  selector: 'lib-contact-details',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, RouterModule],
  templateUrl: 'contact-details.component.html',
  styleUrls: ['contact-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContactDetailsComponent {
  readonly contactDetails$: Observable<ContactDetailsQuery> = this.getsContactDetailsQueryPort.getContactDetailsQuery();

  constructor(
    @Inject(GETS_CONTACT_DETAILS_QUERY) private readonly getsContactDetailsQueryPort: GetsContactDetailsQueryPort,
  ) {}
}
