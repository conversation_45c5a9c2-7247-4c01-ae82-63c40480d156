import { AncillaryBookingsComponent } from './ancillary-bookings.component';
import { TestBed } from '@angular/core/testing';
import { first, of } from 'rxjs';
import {
  GETS_ANCILLARY_BOOKINGS_QUERY,
  GetsAncillaryBookingsQueryPort,
} from '../../../../application/ports/primary/query/gets-ancillary-bookings.query-port';

describe('ActivitiesComponent', () => {
  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [AncillaryBookingsComponent],
      providers: [
        {
          provide: GETS_ANCILLARY_BOOKINGS_QUERY,
          useValue: {
            getAncillaryBookingsQuery: () => of({}),
          } as GetsAncillaryBookingsQueryPort,
        },
      ],
    }).compileComponents();

    return {
      component: TestBed.createComponent(AncillaryBookingsComponent).componentInstance,
    };
  };

  it('should create component', async () => {
    const { component } = await given();

    component.ancillaryBookings$.pipe(first()).subscribe((data) => {
      expect(data).toEqual({});
    });
  });
});
