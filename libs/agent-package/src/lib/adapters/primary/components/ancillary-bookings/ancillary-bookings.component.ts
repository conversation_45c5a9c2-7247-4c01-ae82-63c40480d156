import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import {
  BuyoutDetailsComponent,
  UiBackdropButtonComponent,
  UiBadgeComponent,
  UiSidebarComponentModule,
  UiStatusComponent,
} from '@shared/ui';
import { Observable } from 'rxjs';
import { AncillaryBookingsQuery } from '../../../../application/ports/primary/query/ancillary-bookings.query';
import {
  GETS_ANCILLARY_BOOKINGS_QUERY,
  GetsAncillaryBookingsQueryPort,
} from '../../../../application/ports/primary/query/gets-ancillary-bookings.query-port';
import { BaseBookingDirective } from '../../directives/base-booking/base-booking.directive';
import { BookingPriceComponent } from '../booking-elements/booking-price/booking-price.component';
import { BuyoutComponent } from '../booking-elements/buyout/buyout.component';
import { IssuingAttemptsComponent } from '../issuing-attempts/issuing-attempts.component';

@Component({
  selector: 'lib-ancillary-bookings',
  standalone: true,
  imports: [
    BaseBookingDirective,
    CoreAngularModule,
    CommonModule,
    BookingPriceComponent,
    BuyoutComponent,
    BuyoutDetailsComponent,
    RouterLink,
    UiStatusComponent,
    UiSidebarComponentModule,
    UiBackdropButtonComponent,
    IssuingAttemptsComponent,
    UiBadgeComponent,
  ],
  templateUrl: 'ancillary-bookings.component.html',
  styleUrls: ['ancillary-bookings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AncillaryBookingsComponent {
  readonly ancillaryBookings$: Observable<AncillaryBookingsQuery> =
    this.getAncillaryBookingsQueryPort.getAncillaryBookingsQuery();

  constructor(
    @Inject(GETS_ANCILLARY_BOOKINGS_QUERY)
    private readonly getAncillaryBookingsQueryPort: GetsAncillaryBookingsQueryPort,
  ) {}
}
