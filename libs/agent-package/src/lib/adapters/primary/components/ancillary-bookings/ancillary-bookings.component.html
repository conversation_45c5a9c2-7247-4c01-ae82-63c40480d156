@if (ancillaryBookings$ | async; as ancillaryBookings) {
  @for (ancillaryBooking of ancillaryBookings.ancillaryBookings; track i; let i = $index) {
    <div baseBooking #baseBooking="baseBooking" class="rounded-container" [id]="'ancillaryBookingIndex=' + i">
      @if (!ancillaryBooking.isVisible) {
        <div class="booking-visibility-information">
          <ecs-icon class="icon-xl mr-m status alert" name="eye_crossed" />
          <div class="info">
            <p class="title">Hidden booking</p>
            <p class="description">This booking is not visible for the end user</p>
          </div>
        </div>
      }
      <div class="section-title-container justify-content-sb">
        <div class="title-container" [class.edit-booking]="ancillaryBooking.canEdit">
          <p class="title">Ancillary</p>
          <p class="subtitle mr-l">{{ ancillaryBooking.productId }}</p>
          <ui-status [status]="ancillaryBooking.bookingStatus" />
          @if (ancillaryBooking.tags) {
            @for (tag of ancillaryBooking.tags; track $index) {
              <ui-badge class="ml-l" [text]="tag" />
            }
          }
        </div>
        <div class="d-flex">
          @if (ancillaryBooking.canEdit) {
            <ecs-button kind="tertiary ml-xl" icon="pencil" iconPosition="right" (click)="editOptionsDialog.open()">
              Edit
            </ecs-button>
            <ui-backdrop-button class="ml-xs" size="medium">
              <a
                data-selector="edit-issue-redirect-anchor"
                class="option"
                (click)="
                  baseBooking.openEditPage(
                    ancillaryBooking.canIssue,
                    ancillaryBooking.linkQuery.getEditRouterLink('issue')
                  )
                "
                [class.disabled]="!ancillaryBooking.canIssue"
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !ancillaryBooking.canIssue }"
                  name="check_line"
                ></ecs-icon>
                Issuing
              </a>

              <a
                data-selector="edit-booking-cancellation-anchor"
                class="option"
                [class.disabled]="!ancillaryBooking.canCancel"
                (click)="
                  baseBooking.openEditPage(
                    ancillaryBooking.canCancel,
                    ancillaryBooking.linkQuery.getEditRouterLink('cancellation')
                  )
                "
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !ancillaryBooking.canCancel }"
                  name="action_trashcan"
                ></ecs-icon>
                Cancel
              </a>

              <a
                data-selector="edit-booking-hide-anchor"
                class="option"
                [class.disabled]="!ancillaryBooking.canChangeVisibility"
                (click)="
                  baseBooking.onChangeBookingVisibility(
                    ancillaryBooking.canChangeVisibility,
                    ancillaryBooking.isVisible,
                    ancillaryBooking.productId,
                    ancillaryBooking.itemId
                  )
                "
                #link
                target="_blank"
              >
                @if (ancillaryBooking.isVisible) {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !ancillaryBooking.canChangeVisibility }"
                    name="eye_line_crossed"
                  ></ecs-icon>
                  Hide
                } @else {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !ancillaryBooking.canChangeVisibility }"
                    name="eye_line"
                  ></ecs-icon>
                  Show
                }
              </a>
            </ui-backdrop-button>
          }
        </div>
      </div>
      <div class="booking">
        <div class="booking-main-section-container">
          <div class="booking-main-data-container">
            <div class="booking-main-data-card">
              <div class="content ancillary-booking-content">
                <p class="booking-data-card-title">{{ ancillaryBooking.name }}</p>
                <p class="booking-data-card-additional-info">
                  @if (ancillaryBooking.freeCancellationUntil) {
                    Free cancellation until:
                    <span class="value">{{
                      ancillaryBooking.freeCancellationUntil | date: 'dd MMM (EEE) yyyy' : 'UTC'
                    }}</span>
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="booking-sub-section-container">
          <div class="multi-row-data-container">
            <div class="booking-data-row small">
              <div class="data">
                <p class="label">Provider ID</p>
                <p class="value ellipsis">{{ ancillaryBooking.providerBookingId }}</p>
              </div>
              <div class="data">
                <p class="label">Provider</p>
                <p class="value ellipsis">{{ ancillaryBooking.providerName }}</p>
              </div>
            </div>
            <div class="booking-data-row small">
              <div class="data">
                <p class="label">Created at</p>
                <p class="value ellipsis">{{ ancillaryBooking.createdDate | date: 'dd.MM.yyyy - HH:mm' : 'UTC' }}</p>
              </div>
              <div
                #providerStatusAnchorInDetails
                data-selector="provider-status-anchor-in-details"
                [ngClass]="['data', !!ancillaryBooking.providerAdditionalMessage ? 'cursor-pointer' : '']"
                (click)="
                  !!ancillaryBooking.providerAdditionalMessage &&
                    baseBooking.onProviderStatusPopoverOpen(providerStatusAnchorInDetails)
                "
              >
                <p class="label">Provider status</p>
                <p class="value">{{ ancillaryBooking.providerStatus }}</p>
              </div>
            </div>
          </div>
        </div>

        @if (ancillaryBooking.bookingPrice) {
          <div class="buyouts">
            <lib-booking-price
              [bookingPrice]="ancillaryBooking.bookingPrice"
              totalPriceLabel="Ancillary Price"
            ></lib-booking-price>

            <ecs-link
              class="d-block mt-m"
              size="small"
              data-selector="ancillary-bookings-price-preview-anchor"
              target="_blank"
              [href]="ancillaryBooking.linkQuery.getPreviewRouterLink('price-elements')"
            >
              Provider price preview
            </ecs-link>
            @if (ancillaryBooking.buyouts.length) {
              <div class="mt-xs">
                @for (buyout of ancillaryBooking.buyouts; track $index) {
                  <lib-buyout
                    class="mt-m card-hover-shadow"
                    [index]="$index"
                    [buyout]="buyout"
                    (click)="sidebar.openSidebar()"
                  ></lib-buyout>
                  <ui-sidebar sidebarTitle="Buyout details" #sidebar>
                    <ui-buyout-details [buyout]="buyout" />
                  </ui-sidebar>
                }
              </div>
            }
          </div>
        }
      </div>

      <lib-issuing-attempts [issuingAttempts]="ancillaryBooking.issuingAttempts" />
      <ecs-dialog
        #editOptionsDialog
        class="ecs-dialog-standard"
        data-selector="options-dialog"
        has-backdrop
        (ecsBackdropClick)="editOptionsDialog.close()"
        (ecsEscapeKeydown)="editOptionsDialog.close()"
      >
        <ecs-dialog-header
          class="ecs-header"
          slot="dialog-header"
          [headerTitle]="'Edit ancillary - ' + ancillaryBooking.productId"
          (ecsCloseButtonClick)="editOptionsDialog.close()"
        ></ecs-dialog-header>
        <div class="body" slot="dialog-body">
          <p class="text-2-color mb-xl">Choose which one of element you want to edit</p>
          <div class="d-flex">
            @if (!ancillaryBooking.isEskyAncillary) {
              <div
                class="square-option mr-s"
                data-selector="edit-ancillary-booking-general-anchor"
                [routerLink]="ancillaryBooking.linkQuery.getEditRouterLink('general')"
                (click)="editOptionsDialog.close()"
              >
                <ecs-icon class="icon-xl mb-xs" name="settings_lowercase" />
                <p class="name">General</p>
              </div>
            }
            <div
              class="square-option mr-s"
              data-selector="booking-price-anchor"
              [routerLink]="ancillaryBooking.linkQuery.getEditRouterLink('price-elements')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="money_coins" />
              <p class="name">Price</p>
            </div>

            @if (!ancillaryBookings.showBookingPriceBuyoutAssignment) {
              <div
                class="square-option"
                data-selector="edit-ancillary-booking-buyouts-anchor"
                [routerLink]="ancillaryBooking.linkQuery.getEditRouterLink('buyouts')"
                (click)="editOptionsDialog.close()"
              >
                <ecs-icon class="icon-xl mb-xs" name="buyouts" />
                <p class="name">Buyouts</p>
              </div>
            }
          </div>
        </div>
      </ecs-dialog>
      <div>
        <ecs-popover
          #providerStatusPopover
          data-selector="provider-status-popover"
          id="front-layer"
          [relativePosition]="'right-bottom'"
          [anchorElement]="baseBooking.providerStatusAnchor"
          positionId="provider-additional-message"
        >
          <ecs-popover-header
            data-selector="provider-status-popover-header"
            headerTitle="Provider message"
            slot="popover-header"
            (ecsCloseButtonClick)="baseBooking.onProviderStatusPopoverClose()"
          >
          </ecs-popover-header>
          <div class="popover-body" slot="popover-body" data-selector="provider-status-popover-body">
            {{ ancillaryBooking.providerAdditionalMessage }}
          </div>
        </ecs-popover>
        <ecs-backdrop
          #providerStatusPopoverBackdrop
          class="popover-backdrop"
          (click)="baseBooking.onProviderStatusPopoverClose()"
        ></ecs-backdrop>
      </div>
    </div>
  }
}
