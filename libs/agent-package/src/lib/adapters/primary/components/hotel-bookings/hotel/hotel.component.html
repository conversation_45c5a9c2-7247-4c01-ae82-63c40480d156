<div class="d-flex justify-content-sb booking-data-card-spacer">
  <div class="booking-data-card-dates-container">
    <p class="booking-data-card-date">{{ hotelBooking.checkInDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>
    <img class="date-icon" [src]="'icon_circle_with_arrow_right' | pmsImage" alt="arrow" />
    <p class="booking-data-card-date">{{ hotelBooking.checkOutDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>
  </div>
  @if (showCancellationInfo) {
    <p class="booking-data-card-additional-info">
      Free cancellation until:
      @if (hotelBooking.cancellationDetails?.isFree) {
        <span class="value">{{ hotelBooking.cancellationDetails?.until | date : 'dd MMM (EEE) yyyy' : 'UTC' }}</span>
      } @else {
        -
      }
    </p>
  }
</div>
<p class="booking-data-card-title">{{ hotelBooking.hotel.fullName }}</p>
<p class="booking-data-card-subtitle">{{ hotelBooking.hotel.fullAddress }}</p>
