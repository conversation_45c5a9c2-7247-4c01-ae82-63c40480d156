import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { Observable } from 'rxjs';
import {
  GETS_HOTEL_BOOKINGS_QUERY,
  GetsHotelBookingsQueryPort,
} from '../../../../application/ports/primary/query/gets-hotel-bookings.query-port';
import { HotelBookingsQuery } from '../../../../application/ports/primary/query/hotel-bookings.query';

@Component({
  selector: 'lib-hotel-bookings',
  templateUrl: 'hotel-bookings.component.html',
  styleUrls: ['hotel-bookings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HotelBookingsComponent {
  readonly hotelBookings$: Observable<HotelBookingsQuery> = this.getHotelBookingsQueryPort.getHotelBookingsQuery();

  constructor(
    @Inject(GETS_HOTEL_BOOKINGS_QUERY) private readonly getHotelBookingsQueryPort: GetsHotelBookingsQueryPort,
  ) {}
}
