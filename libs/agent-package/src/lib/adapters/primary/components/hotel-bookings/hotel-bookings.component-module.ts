import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import {
  BuyoutDetailsComponent,
  OpenUiSidebarDirectiveModule,
  UiBackdropButtonComponent,
  UiBadgeComponent,
  UiCopyWithTooltipComponent,
  UiSidebarComponentModule,
  UiStatusComponent,
} from '@shared/ui';
import { BaseBookingDirective } from '../../directives/base-booking/base-booking.directive';
import { BookingPriceComponent } from '../booking-elements/booking-price/booking-price.component';
import { BuyoutComponent } from '../booking-elements/buyout/buyout.component';
import { IssuingAttemptsComponent } from '../issuing-attempts/issuing-attempts.component';
import { ListItemsComponent } from '../list-items/list-items.component';
import { HotelBookingsComponent } from './hotel-bookings.component';
import { HotelComponent } from './hotel/hotel.component';
import { RoomComponent } from './room/room.component';

@NgModule({
  imports: [
    BookingPriceComponent,
    BuyoutComponent,
    BuyoutDetailsComponent,
    CommonModule,
    CoreAngularModule,
    OpenUiSidebarDirectiveModule,
    PmsImagePipeModule,
    RouterLink,
    UiStatusComponent,
    UiBackdropButtonComponent,
    UiCopyWithTooltipComponent,
    UiSidebarComponentModule,
    BaseBookingDirective,
    ListItemsComponent,
    IssuingAttemptsComponent,
    UiBadgeComponent,
  ],
  declarations: [HotelBookingsComponent, RoomComponent, HotelComponent],
  providers: [],
  exports: [HotelBookingsComponent],
})
export class HotelBookingsComponentModule {}
