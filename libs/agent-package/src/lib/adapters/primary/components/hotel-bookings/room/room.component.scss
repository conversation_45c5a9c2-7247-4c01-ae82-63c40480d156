:host {
  display: block;
}

.room-title-container {
  color: var(--text-2-color);
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xxs);
  line-height: var(--line-height-xxs);
}

.room-name {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-xs);
}

.room-details-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: var(--spacing-xs);
  padding-top: var(--spacing-xxs);
  border-top: var(--border-default);

  .room-detail-container {
    display: flex;
    padding-top: var(--spacing-xxs);

    &:not(:last-of-type) {
      margin-right: var(--spacing-s);
    }

    .room-detail-text {
      color: var(--text-2-color);
      white-space: nowrap;
      font-size: var(--font-size-xxs);
      line-height: var(--line-height-xxs);
    }
  }
}
