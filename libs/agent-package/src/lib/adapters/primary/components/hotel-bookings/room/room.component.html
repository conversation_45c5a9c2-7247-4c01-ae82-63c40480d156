<div class="room-title-container">
  <p class="mr-xxs">{{ room.roomTitle }}</p>
  <p>{{ room.mainGuest }}</p>
</div>
<p class="room-name">{{ room.name }}</p>
<div class="room-details-container">
  <div class="room-detail-container">
    <ecs-icon class="icon-xs mr-xxs" name="man" />
    <p class="room-detail-text">Adults: {{ room.adultCount }}</p>
  </div>
  <div class="room-detail-container">
    <ecs-icon class="icon-xs mr-xxs" name="infant" />
    <p class="room-detail-text">
      Children: {{ room.childrenCount }}
      @if (room.canShowChildrenAges) {
        (age: {{ room.childrenAgesString }})
      }
    </p>
  </div>
  <div class="room-detail-container">
    <ecs-icon class="icon-xs mr-xxs" name="bed_double" />
    <p class="room-detail-text">Bed type: {{ room.bedType }}</p>
  </div>
  <div class="room-detail-container">
    <ecs-icon class="icon-xs mr-xxs" name="food" />
    <p class="room-detail-text">Meals: {{ room.mealPlan }}</p>
  </div>
</div>
