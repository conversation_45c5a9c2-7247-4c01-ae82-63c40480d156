import { Component, Input } from '@angular/core';
import { HotelBookingQuery } from '../../../../../application/ports/primary/query/hotel-booking.query';

@Component({
  selector: 'lib-hotel',
  templateUrl: 'hotel.component.html',
  styleUrls: ['hotel.component.scss'],
})
export class HotelComponent {
  @Input({ required: true }) hotelBooking!: HotelBookingQuery;
  @Input() showCancellationInfo = true;
}
