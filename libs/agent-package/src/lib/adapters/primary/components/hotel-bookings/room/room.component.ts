import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RoomQuery } from '../../../../../application/ports/primary/query/room.query';

@Component({
  selector: 'lib-room',
  templateUrl: 'room.component.html',
  styleUrls: ['room.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RoomComponent {
  @Input({ required: true }) room!: RoomQuery;
}
