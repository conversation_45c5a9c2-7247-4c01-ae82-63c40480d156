@if (hotelBookings$ | async; as hotelBookings) {
  @for (hotelBooking of hotelBookings.hotelBookings; track i; let i = $index) {
    <div baseBooking #baseBooking="baseBooking" class="rounded-container" [id]="'hotelBookingIndex=' + i">
      @if (!hotelBooking.isVisible) {
        <div class="booking-visibility-information">
          <ecs-icon class="icon-xl mr-m status alert" name="eye_crossed" />
          <div class="info">
            <p class="title">Hidden booking</p>
            <p class="description">This booking is not visible for the end user</p>
          </div>
        </div>
      }
      <div class="section-title-container justify-content-sb">
        <div class="title-container" [class.edit-booking]="hotelBooking.canEdit">
          <p class="title">Hotel</p>
          <p class="subtitle mr-l">{{ hotelBooking.productId }}</p>
          <ui-status [status]="hotelBooking.bookingStatus" />
          @if (hotelBooking.tags) {
            @for (tag of hotelBooking.tags; track $index) {
              <ui-badge class="ml-l" [text]="tag" />
            }
          }
        </div>
        <div class="d-flex">
          <ecs-button kind="tertiary" (click)="hotelBookingDetailsSidebar.openSidebar()">Show details</ecs-button>

          @if (hotelBooking.canEdit) {
            <ecs-button kind="tertiary ml-xl" icon="pencil" iconPosition="right" (click)="editOptionsDialog.open()">
              Edit
            </ecs-button>
            <ui-backdrop-button class="ml-xs" size="medium">
              <a
                data-selector="edit-issue-redirect-anchor"
                class="option"
                (click)="
                  baseBooking.openEditPage(hotelBooking.canIssue, hotelBooking.linkQuery.getEditRouterLink('issue'))
                "
                [class.disabled]="!hotelBooking.canIssue"
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !hotelBooking.canIssue }"
                  name="check_line"
                ></ecs-icon>
                Issuing
              </a>

              <a
                data-selector="edit-booking-cancellation-anchor"
                class="option"
                [class.disabled]="!hotelBooking.canCancel"
                (click)="
                  baseBooking.openEditPage(
                    hotelBooking.canCancel,
                    hotelBooking.linkQuery.getEditRouterLink('cancellation')
                  )
                "
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !hotelBooking.canCancel }"
                  name="action_trashcan"
                ></ecs-icon>
                Cancel
              </a>

              <a
                data-selector="edit-booking-hide-anchor"
                class="option"
                [class.disabled]="!hotelBooking.canChangeVisibility"
                (click)="
                  baseBooking.onChangeBookingVisibility(
                    hotelBooking.canChangeVisibility,
                    hotelBooking.isVisible,
                    hotelBooking.productId,
                    hotelBooking.itemId
                  )
                "
                #link
                target="_blank"
              >
                @if (hotelBooking.isVisible) {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !hotelBooking.canChangeVisibility }"
                    name="eye_line_crossed"
                  ></ecs-icon>
                  Hide
                } @else {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !hotelBooking.canChangeVisibility }"
                    name="eye_line"
                  ></ecs-icon>
                  Show
                }
              </a>
            </ui-backdrop-button>
          }
        </div>
      </div>
      <div class="booking">
        <div class="booking-main-section-container">
          <div class="data-container mb-xl">
            <div class="data">
              <ui-copy-with-tooltip [textToCopy]="hotelBooking.providerBookingId">
                <p class="label">Provider ID</p>
                <p class="value">{{ hotelBooking.providerBookingId }}</p>
              </ui-copy-with-tooltip>
            </div>
            <div class="data">
              <p class="label">Provider</p>
              <p class="value">{{ hotelBooking.providerName }}</p>
            </div>
            <div class="data">
              <p class="label">Product ID</p>
              <p class="value">{{ hotelBooking.productId }}</p>
            </div>
            <div class="data">
              <p class="label">Hotel type</p>
              <p class="value">{{ hotelBooking.hotelType }}</p>
            </div>
            <div
              #providerStatusAnchorInDetails
              [ngClass]="['data', !!hotelBooking.providerAdditionalMessage ? 'cursor-pointer' : '']"
              (click)="
                !!hotelBooking.providerAdditionalMessage &&
                  baseBooking.onProviderStatusPopoverOpen(providerStatusAnchorInDetails)
              "
            >
              <p class="label">Provider status</p>
              <p class="value">{{ hotelBooking.providerStatus }}</p>
            </div>
          </div>
          <div class="booking-main-data-container">
            <div class="booking-main-data-card">
              <div class="content">
                <lib-hotel [hotelBooking]="hotelBooking" />
                <ui-sidebar #hotelBookingDetailsSidebar [sidebarTitle]="'Hotel - ' + hotelBooking.productId">
                  <div class="sidebar-content-container">
                    <lib-hotel [hotelBooking]="hotelBooking" [showCancellationInfo]="false" />

                    <lib-list-items class="d-block mt-xl" [items]="hotelBooking.detailsItems" />
                    <p class="sidebar-booking-detail-title">Stays</p>
                    <div class="multi-row-data-container mb-xxl-2">
                      <div class="booking-data-row">
                        <div class="data">
                          <p class="label">Check-in</p>
                          <p class="value">{{ hotelBooking.checkInDate | date: 'dd.MM.yyyy' : 'UTC' }}</p>
                        </div>
                        <div class="data">
                          <p class="label">Check-out</p>
                          <p class="value">{{ hotelBooking.checkOutDate | date: 'dd.MM.yyyy' : 'UTC' }}</p>
                        </div>
                      </div>
                      <div class="booking-data-row">
                        <div class="data">
                          <p class="label">Night</p>
                          <p class="value">{{ hotelBooking.nights }}</p>
                        </div>
                        <div class="data">
                          <p class="label">Guest</p>
                          <p class="value">{{ hotelBooking.guest }}</p>
                        </div>
                      </div>
                    </div>
                    <p class="sidebar-booking-detail-title">Creation</p>
                    <div class="multi-row-data-container">
                      <div class="booking-data-row">
                        <div class="data">
                          <p class="label">Created at</p>
                          <p class="value">{{ hotelBooking.createdDate | date: 'dd.MM.yyyy - HH:mm' : 'UTC' }}</p>
                        </div>
                      </div>
                    </div>
                    <div class="sidebar-booking-section-spacer"></div>
                    <p class="sidebar-booking-detail-title">Rooms</p>
                    <div class="mb-xxl-2">
                      <lib-room
                        *ngFor="let room of hotelBooking.hotel.rooms"
                        class="sidebar-booking-data-card"
                        [room]="room"
                      ></lib-room>
                    </div>
                    <p class="sidebar-booking-detail-title">Another info</p>
                    <div class="multi-row-data-container mb-xxl-2">
                      <div class="booking-data-row">
                        <div class="data">
                          <p class="label">Free cancellation until</p>
                          <p class="value">
                            <span
                              class="free-cancellation-date"
                              *ngIf="
                                hotelBooking.cancellationDetails && hotelBooking.cancellationDetails.isFree;
                                else noFreeCancellationDate
                              "
                            >
                              {{ hotelBooking.cancellationDetails.until | date: 'dd MMM (EEE) yyyy' : 'UTC' }}
                            </span>
                            <ng-template #noFreeCancellationDate> -</ng-template>
                          </p>
                        </div>
                        <div class="data">
                          <p class="label">Room confirmation number</p>
                          <p class="value">{{ hotelBooking.hotelConfirmationNumber }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </ui-sidebar>
              </div>
            </div>
          </div>
        </div>
        <div class="booking-sub-section-container">
          <div *ngFor="let room of hotelBooking.hotel.rooms" class="booking-sub-section-card">
            <lib-room [room]="room" />
          </div>
        </div>
        @if (hotelBooking.bookingPrice) {
          <div class="buyouts">
            <lib-booking-price [bookingPrice]="hotelBooking.bookingPrice" totalPriceLabel="Hotel Price" />

            <ecs-link
              class="d-block mt-m"
              data-selector="hotel-bookings-price-preview-anchor"
              size="small"
              target="_blank"
              [href]="hotelBooking.linkQuery.getPreviewRouterLink('price-elements')"
            >
              Provider price preview
            </ecs-link>
            @if (hotelBooking.buyouts.length) {
              <div class="mt-xs">
                @for (buyout of hotelBooking.buyouts; track $index) {
                  <lib-buyout
                    class="mt-m card-hover-shadow"
                    [buyout]="buyout"
                    [index]="$index"
                    (click)="sidebar.openSidebar()"
                  ></lib-buyout>
                  <ui-sidebar sidebarTitle="Buyout details" #sidebar>
                    <ui-buyout-details [buyout]="buyout" />
                  </ui-sidebar>
                }
              </div>
            }
          </div>
        }
      </div>

      <lib-issuing-attempts [issuingAttempts]="hotelBooking.issuingAttempts" />
      <ecs-dialog
        #editOptionsDialog
        class="ecs-dialog-standard"
        data-selector="options-dialog"
        has-backdrop
        (ecsBackdropClick)="editOptionsDialog.close()"
        (ecsEscapeKeydown)="editOptionsDialog.close()"
      >
        <ecs-dialog-header
          class="ecs-header"
          slot="dialog-header"
          [headerTitle]="'Edit hotel - ' + hotelBooking.productId"
          (ecsCloseButtonClick)="editOptionsDialog.close()"
        ></ecs-dialog-header>
        <div class="body" slot="dialog-body">
          <p class="text-2-color mb-xl">Choose which one of element you want to edit</p>
          <div class="d-flex">
            <div
              class="square-option mr-s"
              data-selector="edit-hotel-booking-general-anchor"
              [routerLink]="hotelBooking.linkQuery.getEditRouterLink('general')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="settings_lowercase" />
              <p class="name">General</p>
            </div>
            <div
              class="square-option mr-s"
              data-selector="edit-hotel-booking-price-anchor"
              [routerLink]="hotelBooking.linkQuery.getEditRouterLink('price-elements')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="money_coins" />
              <p class="name">Price</p>
            </div>

            @if (!hotelBookings.showBookingPriceBuyoutAssignment) {
              <div
                class="square-option"
                data-selector="edit-hotel-booking-buyouts-anchor"
                [routerLink]="hotelBooking.linkQuery.getEditRouterLink('buyouts')"
                (click)="editOptionsDialog.close()"
              >
                <ecs-icon class="icon-xl mb-xs" name="buyouts" />
                <p class="name">Buyouts</p>
              </div>
            }
          </div>
        </div>
      </ecs-dialog>
      <div>
        <ecs-popover
          #providerStatusPopover
          data-selector="provider-status-popover"
          id="front-layer"
          [relativePosition]="'right-bottom'"
          [anchorElement]="baseBooking.providerStatusAnchor"
          positionId="provider-additional-message"
        >
          <ecs-popover-header
            data-selector="provider-status-popover-header"
            headerTitle="Provider message"
            slot="popover-header"
            (ecsCloseButtonClick)="baseBooking.onProviderStatusPopoverClose()"
          >
          </ecs-popover-header>
          <div class="popover-body" slot="popover-body" data-selector="provider-status-popover-body">
            {{ hotelBooking.providerAdditionalMessage }}
          </div>
        </ecs-popover>
        <ecs-backdrop
          #providerStatusPopoverBackdrop
          (click)="baseBooking.onProviderStatusPopoverClose()"
        ></ecs-backdrop>
      </div>
    </div>
  }
}
