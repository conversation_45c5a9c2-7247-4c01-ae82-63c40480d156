<form [formGroup]="activityForm" class="activities-form-container" (ngSubmit)="onActivityFormSubmitted()">
  <div class="mb-xxl-2">
    <p class="label">Type of contact<span class="red-star">*</span></p>
    <ecs-radio-group formControlName="contactType" class="d-flex" data-selector="activity-contact-type-radio">
      <ecs-radio class="mr-l" value="phone" label="Phone" />
      <ecs-radio value="email" label="E-mail" />
    </ecs-radio-group>
    <p class="form-error-message" *ngIf="activityForm.touched && !contactTypeFormControl.valid">
      Type of contact is required
    </p>
  </div>
  <div class="mb-xxl-2">
    <label for="purecloud" class="label">Purecloud link</label>
    <ecs-input
      id="purecloud"
      class="w-100"
      data-selector="activity-purecloud-url-input"
      size="large"
      formControlName="pureCloudUrl"
      placeholder="Paste purecloud link here"
    ></ecs-input>
  </div>
  <div class="mb-xxl-2">
    <label for="request" class="label">Request<span class="red-star">*</span></label>
    <textarea
      formControlName="request"
      id="request"
      data-selector="activity-request-textarea"
      type="text"
      placeholder="Write more about contact reason"
    ></textarea>
    <p class="form-error-message" *ngIf="!requestFormControl.valid && requestFormControl.touched">
      <ng-container *ngIf="requestFormControl.hasError('required')"> Request is required </ng-container>
      <ng-container *ngIf="requestFormControl.hasError('minlength')">
        Request must have at least 3 characters
      </ng-container>
    </p>
  </div>
  <div class="mb-xxl-2">
    <label for="result" class="label">Result</label>
    <textarea
      formControlName="result"
      id="result"
      data-selector="activity-result-textarea"
      type="text"
      placeholder="What was the result of the contact"
    ></textarea>
  </div>
  <div class="mb-xxl-2">
    <label for="anotherUrl" class="label">Another link</label>
    <ecs-input
      id="anotherUrl"
      class="w-100"
      data-selector="activity-another-link-input"
      size="large"
      formControlName="anotherUrl"
      placeholder="Paste link here"
    ></ecs-input>
  </div>
  <div class="mt-auto pb-xxl-2">
    <ecs-button
      class="w-100"
      kind="primary"
      size="large"
      outline="true"
      type="reset"
      data-selector="activity-reset-button"
      [disabled]="isLoading"
      (click)="resetForm()"
    >
      Reset
    </ecs-button>
    <ecs-button
      class="w-100 mt-s"
      size="large"
      kind="primary"
      type="submit"
      data-selector="activity-save-button"
      [disabled]="!activityForm.valid || isLoading"
    >
      Save
    </ecs-button>
  </div>
</form>
