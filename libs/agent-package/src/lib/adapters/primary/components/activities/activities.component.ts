import { ChangeDetectionStrategy, Component, Inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreAngularModule } from '@ecs/angular';
import { OpenUiSidebarDirectiveModule, UiSidebarComponent, UiSidebarComponentModule } from '@shared/ui';
import { ActivityFormComponent } from './activity-form/activity-form.component';
import { ActivityListComponent } from './activity-list/activity-list.component';
import { Observable, tap } from 'rxjs';
import {
  GETS_ACTIVITIES_QUERY,
  GetsActivitiesQueryPort,
} from '../../../../application/ports/primary/query/gets-activities.query-port';
import { ActivitiesQuery } from '../../../../application/ports/primary/query/activities.query';
import {
  CREATE_ACTIVITY_COMMAND,
  CreateActivityCommandPort,
} from '../../../../application/ports/primary/command/create-activity.command-port';
import { take } from 'rxjs/operators';
import { ActivityFormView } from './activity-form/activity-form.view';
import { AbortOperationComponent } from '../abort-operation/abort-operation.component';
import {
  ABORT_PROCESS_COMMAND,
  AbortProcessCommandPort,
} from '../../../../application/ports/primary/command/abort-process.command-port';

@Component({
  selector: 'lib-activities',
  standalone: true,
  imports: [
    CommonModule,
    CoreAngularModule,
    OpenUiSidebarDirectiveModule,
    UiSidebarComponentModule,
    ActivityFormComponent,
    ActivityListComponent,
    AbortOperationComponent,
  ],
  templateUrl: 'activities.component.html',
  styleUrls: ['activities.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivitiesComponent {
  @ViewChild('addActivitySidebar') addActivitySidebar!: UiSidebarComponent;
  @ViewChild('activityForm') activityForm!: ActivityFormComponent;
  @ViewChild('abortOperationDialog') abortOperationDialog!: AbortOperationComponent;

  readonly activitiesQuery$: Observable<ActivitiesQuery> = this.getsActivitiesQueryPort.getActivitiesQuery();

  constructor(
    @Inject(GETS_ACTIVITIES_QUERY) private readonly getsActivitiesQueryPort: GetsActivitiesQueryPort,
    @Inject(CREATE_ACTIVITY_COMMAND) private readonly createActivityCommandPort: CreateActivityCommandPort,
    @Inject(ABORT_PROCESS_COMMAND) private readonly abortProcessCommand: AbortProcessCommandPort
  ) {}

  createActivity(activity: ActivityFormView): void {
    this.createActivityCommandPort
      .createActivity(activity)
      .pipe(
        take(1),
        tap(() => {
          this.addActivitySidebar.closeSidebar();
          this.activityForm.resetForm();
        })
      )
      .subscribe();
  }

  onAbortOperation(reason: string): void {
    this.abortProcessCommand
      .abortProcess({ reason })
      .pipe(
        take(1),
        tap(() => {
          this.abortOperationDialog.setOperationInProgress();
        })
      )
      .subscribe();
  }
}
