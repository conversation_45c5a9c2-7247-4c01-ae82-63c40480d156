@if (activitiesListQuery$ | async; as activitiesList) {
  <ng-container *ngIf="activitiesList.hasActivity; else noActivity">
    <ng-container *ngIf="activitiesList.forcedImportActivity as forcedImportActivity">
      <p class="activities-title mb-xxl-2">Import from PPS</p>
      <div class="activity">
        <div class="creation-data-container">
          <p class="user">{{ forcedImportActivity.userEmail }}</p>
          <p class="date">{{ forcedImportActivity.createdAt | date: 'HH:mm, dd.MM.yyyy' : 'UTC' }}</p>
        </div>
        <div class="activity-info-container">
          <div class="d-flex">
            <ecs-icon class="icon-xs mr-xs" [name]="forcedImportActivity.icon | ecsIconFilename" />
            <div>
              <p class="activity-type-label">Reason</p>
              <p class="activity-type-value">{{ forcedImportActivity.reason }}</p>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="activitiesList.hasPaxContactActivities">
      <p class="activities-title mb-xxl-2" [ngClass]="{ 'border-top': activitiesList.forcedImportActivity }">
        Activities
      </p>
      <div class="activity" *ngFor="let paxContactActivity of activitiesList.paxContactActivities">
        <div class="creation-data-container">
          <p class="user">{{ paxContactActivity.userEmail }}</p>
          <p class="date">{{ paxContactActivity.createdAt | date: 'HH:mm, dd.MM.yyyy' : 'UTC' }}</p>
        </div>
        <div class="activity-info-container">
          <div class="d-flex">
            <ecs-icon class="icon-xs mr-xs" [name]="paxContactActivity.icon | ecsIconFilename" />
            <div>
              <p class="activity-type-label">Request</p>
              <p class="activity-type-value">{{ paxContactActivity.reason }}</p>
            </div>
          </div>
        </div>
        @if (paxContactActivity.result) {
          <div class="activity-info-container">
            <div class="d-flex">
              <ecs-icon class="icon-xs mr-xs" name="arrow_back" />
              <div>
                <p class="activity-type-label">Result</p>
                <p class="activity-type-value">{{ paxContactActivity.result }}</p>
              </div>
            </div>
          </div>
        }
        <div class="link-container" *ngIf="paxContactActivity.pureCloudUrl || paxContactActivity.additionalUrl">
          <a
            class="link"
            [href]="paxContactActivity.pureCloudUrl | url"
            target="_blank"
            data-selector="activity-list-purecloud-link"
            *ngIf="paxContactActivity.pureCloudUrl"
          >
            Purecloud link
          </a>
          <a
            class="link"
            [href]="paxContactActivity.additionalUrl | url"
            target="_blank"
            data-selector="activity-list-additional-link"
            *ngIf="paxContactActivity.additionalUrl"
          >
            Another link
          </a>
        </div>
      </div>
    </ng-container>
  </ng-container>
  <ng-template #noActivity>
    <p class="activities-title">No activities found</p>
  </ng-template>
}
