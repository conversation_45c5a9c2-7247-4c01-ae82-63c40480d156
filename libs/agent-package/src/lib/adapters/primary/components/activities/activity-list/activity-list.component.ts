import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { EcsIconFilenamePipe, UrlPipeModule } from '@shared/ui';
import { Observable } from 'rxjs';
import { ActivitiesListQuery } from '../../../../../application/ports/primary/query/activities-list.query';
import {
  GETS_ACTIVITIES_LIST_QUERY,
  GetsActivitiesListQueryPort,
} from '../../../../../application/ports/primary/query/gets-activities-list.query-port';

@Component({
  selector: 'lib-activity-list',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, EcsIconFilenamePipe, UrlPipeModule],
  templateUrl: 'activity-list.component.html',
  styleUrls: ['activity-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivityListComponent {
  readonly activitiesListQuery$: Observable<ActivitiesListQuery> =
    this.getsActivitiesQueryPort.getActivitiesListQuery();

  constructor(
    @Inject(GETS_ACTIVITIES_LIST_QUERY) private readonly getsActivitiesQueryPort: GetsActivitiesListQueryPort,
  ) {}
}
