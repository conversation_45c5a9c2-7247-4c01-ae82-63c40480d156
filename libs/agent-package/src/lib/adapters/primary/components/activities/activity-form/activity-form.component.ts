import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CoreAngularModule } from '@ecs/angular';
import { ActivityFormView } from './activity-form.view';

@Component({
  selector: 'lib-activity-form',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, ReactiveFormsModule],
  templateUrl: 'activity-form.component.html',
  styleUrls: ['activity-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivityFormComponent {
  @Output() activityFormSubmitted: EventEmitter<ActivityFormView> = new EventEmitter<ActivityFormView>();

  activityForm: FormGroup = new FormGroup({
    contactType: new FormControl<'phone' | 'email' | ''>('', [Validators.required]),
    pureCloudUrl: new FormControl<string>(''),
    request: new FormControl<string>('', [Validators.required, Validators.minLength(3)]),
    result: new FormControl<string>(''),
    anotherUrl: new FormControl<string>(''),
  });
  requestFormControl: FormControl = this.activityForm.controls['request'] as FormControl;
  contactTypeFormControl: FormControl = this.activityForm.controls['contactType'] as FormControl;

  isLoading = false;

  onActivityFormSubmitted(): void {
    if (!this.activityForm.valid) {
      return;
    }

    this.isLoading = true;
    this.activityFormSubmitted.emit(this.activityForm.value);
  }

  resetForm(): void {
    this.isLoading = false;
    this.activityForm.reset();
  }
}
