import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { ActivityFormComponent } from './activity-form.component';
import { ActivityFormComponentHarness } from './activity-form.component-harness';
import { ActivityFormView } from './activity-form.view';

describe('ActivityFormComponent', () => {
  const ACTIVITY_FORM_VIEW_STUB: ActivityFormView = {
    contactType: 'email',
    pureCloudUrl: '__PURE_CLOUD_URL__',
    request: '__REQUEST__',
    result: '__RESULT__',
    anotherUrl: '__ANOTHER_URL__',
  };

  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [ActivityFormComponent, ReactiveFormsModule],
      declarations: [TestPage],
    }).compileComponents();

    const fixture = TestBed.createComponent(TestPage);
    const loader = TestbedHarnessEnvironment.loader(fixture);
    const componentHarness = await loader.getHarness(ActivityFormComponentHarness);
    const testComponent = fixture.componentInstance;
    const activityFormComponent: ActivityFormComponent = fixture.debugElement.query(
      By.directive(ActivityFormComponent),
    ).componentInstance;

    return {
      componentHarness,
      testPageOnActivityFormSubmittedSpy: jest.spyOn(testComponent, 'onActivityFormSubmitted'),
      activityFormComponent,
    };
  };

  [
    {
      whenData: ACTIVITY_FORM_VIEW_STUB,
      thenData: { emittedValues: ACTIVITY_FORM_VIEW_STUB, isLoading: true },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should submit activity given all values #${i + 1}`, async () => {
      const { testPageOnActivityFormSubmittedSpy, activityFormComponent } = await given();

      activityFormComponent.activityForm.patchValue(whenData);
      activityFormComponent.onActivityFormSubmitted();

      expect(testPageOnActivityFormSubmittedSpy).toHaveBeenCalledWith(thenData.emittedValues);
      expect(activityFormComponent.isLoading).toEqual(thenData.isLoading);
    }),
  );

  [
    {
      whenData: ACTIVITY_FORM_VIEW_STUB,
      thenData: {
        emittedValues: {
          ...ACTIVITY_FORM_VIEW_STUB,
          pureCloudUrl: '',
          result: '',
          anotherUrl: '',
        },
      },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should submit activity given required values #${i + 1}`, async () => {
      const { componentHarness, testPageOnActivityFormSubmittedSpy, activityFormComponent } = await given();

      await componentHarness.fillInput('activity-contact-type-radio', whenData.contactType);
      await componentHarness.fillInput('activity-request-textarea', whenData.request);

      activityFormComponent.onActivityFormSubmitted();

      expect(testPageOnActivityFormSubmittedSpy).toHaveBeenCalledWith(thenData.emittedValues);
    }),
  );

  it(`should not submit activity given not required values`, async () => {
    const { testPageOnActivityFormSubmittedSpy, activityFormComponent } = await given();

    activityFormComponent.onActivityFormSubmitted();

    expect(testPageOnActivityFormSubmittedSpy).not.toHaveBeenCalled();
  });

  [
    {
      whenData: {
        activityFormValue: ACTIVITY_FORM_VIEW_STUB,
        isLoading: true,
      },
      thenData: {
        isLoading: false,
      },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should handle reset form #${i + 1}`, async () => {
      const { activityFormComponent } = await given();

      activityFormComponent.activityForm.patchValue(whenData.activityFormValue);
      activityFormComponent.isLoading = whenData.isLoading;
      activityFormComponent.resetForm();

      expect(activityFormComponent.isLoading).toEqual(thenData.isLoading);
    }),
  );
});

@Component({
  template: '<lib-activity-form (activityFormSubmitted)="onActivityFormSubmitted($event)" />',
})
class TestPage {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onActivityFormSubmitted(activityFormView: ActivityFormView): void {}
}
