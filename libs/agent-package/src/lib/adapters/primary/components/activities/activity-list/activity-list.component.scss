:host {
  display: block;
}

.activity {
  max-width: 612px;

  + .activity {
    margin-top: var(--spacing-l);
    padding-top: var(--spacing-l);
    border-top: 1px solid var(--color-gray-05);
  }

  .creation-data-container {
    display: flex;
    width: 100%;
    font-size: var(--font-size-xs);

    .user {
      font-weight: var(--font-weight-bold);
      margin-right: var(--spacing-xs);
    }

    .date {
      color: var(--text-3-color);
    }
  }

  .activity-info-container {
    margin-top: var(--spacing-s);
    padding: var(--spacing-s);
    background-color: var(--color-gray-0);
    border-radius: var(--border-radius-xs);
    width: fit-content;

    .activity-type-label {
      font-size: var(--font-size-xxs);
      line-height: var(--line-height-xxs);
      color: var(--color-gray-90);
    }

    .activity-type-value {
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
      word-break: break-all;
    }
  }

  .link-container {
    margin-top: var(--spacing-s);

    .link {
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
      margin-right: var(--spacing-s);
      color: var(--color-primary-50);

      + .link {
        padding-left: var(--spacing-s);
        border-left: 1px solid var(--color-gray-05);
      }
    }
  }
}

.border-top {
  margin-top: var(--spacing-l);
  padding-top: var(--spacing-l);
  border-top: 1px solid var(--color-gray-05);
}

.activities-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  line-height: 24px;
}
