import { TestBed } from '@angular/core/testing';
import { ActivitiesComponent } from './activities.component';
import {
  GETS_ACTIVITIES_QUERY,
  GetsActivitiesQueryPort,
} from '../../../../application/ports/primary/query/gets-activities.query-port';
import {
  CREATE_ACTIVITY_COMMAND,
  CreateActivityCommandPort,
} from '../../../../application/ports/primary/command/create-activity.command-port';
import { of } from 'rxjs';
import { ActivityFormView } from './activity-form/activity-form.view';
import {
  ABORT_PROCESS_COMMAND,
  AbortProcessCommandPort,
} from '../../../../application/ports/primary/command/abort-process.command-port';

describe('ActivitiesComponent', () => {
  const ACTIVITY_STUB: ActivityFormView = {
    contactType: 'email',
    pureCloudUrl: '__PURE_CLOUD_URL__',
    request: '__REQUEST__',
    result: '__RESULT__',
    anotherUrl: '__ANOTHER_URL__',
  };

  const given = async () => {
    await TestBed.configureTestingModule({
      imports: [ActivitiesComponent],
      providers: [
        {
          provide: GETS_ACTIVITIES_QUERY,
          useValue: {
            getActivitiesQuery: () => of({ isPackageAvailable: true }),
          } as GetsActivitiesQueryPort,
        },
        {
          provide: CREATE_ACTIVITY_COMMAND,
          useValue: {
            createActivity: () => of(void 0),
          } as CreateActivityCommandPort,
        },
        {
          provide: ABORT_PROCESS_COMMAND,
          useValue: {
            abortProcess: () => of(void 0),
          } as AbortProcessCommandPort,
        },
      ],
    }).compileComponents();

    return {
      activitiesComponent: TestBed.createComponent(ActivitiesComponent).componentInstance,
      createActivityCommandSpy: jest.spyOn(TestBed.inject(CREATE_ACTIVITY_COMMAND), 'createActivity'),
      abortProcessCommandSpy: jest.spyOn(TestBed.inject(ABORT_PROCESS_COMMAND), 'abortProcess'),
    };
  };

  [
    {
      whenData: ACTIVITY_STUB,
      thenData: ACTIVITY_STUB,
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should create activity #${i + 1}`, async () => {
      const { activitiesComponent, createActivityCommandSpy } = await given();

      activitiesComponent.createActivity(whenData);

      expect(createActivityCommandSpy).toHaveBeenCalledWith(thenData);
    }),
  );

  [
    {
      whenData: '__REASON__',
      thenData: { reason: '__REASON__' },
    },
  ].forEach(({ whenData, thenData }, i) =>
    it(`should abort process #${i + 1}`, async () => {
      const { activitiesComponent, abortProcessCommandSpy } = await given();

      activitiesComponent.onAbortOperation(whenData);

      expect(abortProcessCommandSpy).toHaveBeenCalledWith(thenData);
    }),
  );
});
