<ng-container *ngIf="activitiesQuery$ | async as activities">
  <ng-container *ngIf="activities.isPackageAvailable">
    <div class="actions-container">
      <lib-activity-list class="mb-xxl-2" />
      <div class="d-flex justify-content-sb">
        <ecs-button
          size="small"
          kind="primary"
          outline="true"
          type="button"
          data-selector="add-activity-sidebar-open-button"
          [openUiSidebar]="addActivitySidebar"
        >
          Add PAX contact
        </ecs-button>
        <ecs-button
          *ngIf="activities.isAbortOperationAvailable"
          size="small"
          kind="tertiary"
          outline="true"
          type="button"
          data-selector="abort-operation-dialog-open-button"
          (click)="abortOperationDialog.open()"
        >
          Force import to BMS
        </ecs-button>
      </div>
    </div>
    <lib-abort-operation #abortOperationDialog (abortConfirmed)="onAbortOperation($event)" />
    <ui-sidebar #addActivitySidebar sidebarTitle="Add PAX contact">
      <lib-activity-form #activityForm (activityFormSubmitted)="createActivity($event)" />
    </ui-sidebar>
  </ng-container>
</ng-container>
