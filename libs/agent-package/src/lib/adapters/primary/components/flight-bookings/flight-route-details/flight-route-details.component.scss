:host {
  display: block;
}

.icon-clock {
  margin: var(--spacing-xxs) var(--spacing-xs) var(--spacing-xxs) var(--spacing-xxs);
}

.icon-plane {
  align-self: center;
}

.stopover-label {
  margin-top: var(--spacing-xxs);
  font-size: var(--font-size-s);
  line-height: 1.5;
}

.segment-section {
  display: flex;
  position: relative;

  .segment-datetime {
    width: 50px;
    min-width: 50px;
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    font-size: var(--font-size-s);

    .segment-time-label {
      font-size: var(--font-size-s);
      font-weight: var(--font-weight-bold);
    }
  }
}

.line-container {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  margin-left: 13px;
}

.line {
  width: 1px;
  min-height: 16px;
  height: 100%;
  transform: none;
}

.line-margin-left {
  margin-left: 50px;
}

.line-border-aqua {
  border-right: 1px solid var(--color-aqua-blue-10);
}

.line-border-warning {
  border-right: 1px dashed var(--color-warning);
}

.stopover-line-height {
  height: 20px;
}

.dot-margin {
  height: calc(100% - 18px);
  margin-top: 18px;
}

.dot-margin-small {
  height: calc(100% - 16px);
  margin: 16px 0 2px 50px;
}

.line-small {
  height: calc(100% - 27px);
  margin-top: 27px;
  min-height: 0;
}

.segment-info {
  display: flex;
  padding-bottom: var(--spacing-s);
}

.text-wrapper-col {
  display: flex;
  flex-direction: column;
  font-size: var(--font-size-s);
  line-height: 1.5;
  word-wrap: break-word;

  .airport {
    font-size: var(--font-size-s);
    font-weight: var(--font-weight-bold);
  }
}

.text-wrapper-row {
  font-size: var(--font-size-s);
  line-height: 1.5;
  word-wrap: break-word;
}

.segment-details {
  display: flex;
  flex-direction: column;
  padding-bottom: var(--spacing-s);

  .row {
    display: flex;

    .text {
      padding-left: var(--spacing-xs);
    }
  }
}

.row-with-icon {
  display: flex;
  margin-bottom: var(--spacing-m);
}

.no-padding {
  padding: 0;
}

.logo {
  width: 40px;
  height: 40px;
}

.aqua-dot {
  width: 7px;
  height: 7px;
  min-width: 7px;
  min-height: 7px;
  border-radius: 100%;
  background: var(--color-aqua-blue-10);
  margin: 6px 15px 0 10px;
}
