@if (!!segments.length) {
  <div data-selector="flight-route-details">
    <div class="position-relative" *ngFor="let segment of segments; let segmentIndex = index; let lastSegment = last">
      <div class="segment-section">
        <div class="segment-datetime">
          <p class="segment-time-label">{{ segment.departureDate | date : 'HH:mm' : 'UTC' }}</p>
          <p>{{ segment.departureDate | date : 'd MMM' : 'UTC' }}</p>
        </div>
        <div class="line-container">
          <div class="line line-border-aqua dot-margin line-margin-left"></div>
        </div>
        <div class="position-relative">
          <div class="segment-info">
            <div class="aqua-dot"></div>
            <div class="text-wrapper-col">
              <p class="airport">{{ segment.departureAirportName }} ({{ segment.departureAirportCode }})</p>
              <p>{{ segment.departureCity | titlecase }}, {{ segment.departureCountry | titlecase }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="segment-section">
        <div class="segment-datetime">
          <img
            class="logo"
            [src]="'al_logo_signet_' + segment.airlineCode | pmsImage : { format: 'png?s=40x40' }"
            alt="logo"
          />
        </div>
        <div class="line-container">
          <div class="line line-border-aqua line-margin-left"></div>
        </div>
        <div class="segment-details" [ngClass]="{ 'no-padding': segment.hasStopovers }">
          <div class="ml-xxl-2">
            <div class="row">
              <p class="text-wrapper-row">
                Flight duration: <b>{{ segment.flightTimeConverted }}</b>
              </p>
            </div>
            <div class="row">
              <p class="text-wrapper-row">
                Class: <b>{{ segment.serviceClass }}</b>
              </p>
            </div>
            <div class="row">
              <p class="text-wrapper-row">
                Flight number: <b>{{ segment.flightNumber }}</b>
              </p>
            </div>
            <div class="row">
              <p class="text-wrapper-row">
                Airline: <b>{{ segment.airline }}</b>
              </p>
            </div>
            <div class="row">
              <p class="text-wrapper-row">
                Operated by: <b>{{ segment.operatedByAirline }}</b>
              </p>
            </div>
            <div class="row" *ngIf="segment.aircraftCode">
              <ng-container *ngIf="!segment.additionalTransport; else additionalTransport">
                <div class="text-wrapper-row d-flex">
                  <ecs-icon class="icon-xs icon-plane" name="plane_right" />
                  <span class="text">{{ segment.aircraftCode }}</span>
                </div>
              </ng-container>
              <ng-template #additionalTransport>
                <div class="text-wrapper-row d-flex">
                  <ecs-icon class="icon-xs icon-plane"
                            [name]="segment.additionalTransport.iconName || '' | ecsIconFilename" />
                  <span class="text">{{ segment.additionalTransport.transport }}</span>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
      @if (segment.hasStopovers) {
        <ng-container *ngFor="let stopover of segment.stopovers">
          <div class="segment-section mb-xxs mt-xxs">
            <div class="segment-datetime"></div>
            <div class="line-container">
              <div class="line line-border-warning line-margin-left stopover-line-height"></div>
            </div>
            <div class="row-with-icon"></div>
          </div>
          <div class="segment-section">
            <div class="segment-datetime"></div>
            <div class="line-container">
              <div class="line line-border-warning line-margin-left line-small"></div>
            </div>
            <div class="row-with-icon">
              <img class="icon-m icon-clock" [src]="'icon_clock' | pmsImage : { color: 'e0760b' }" alt="clock" />
              <span>
              <p class="stopover-label">{{ stopover.stopoverLabel }}</p>
            </span>
            </div>
          </div>
        </ng-container>
        <div class="segment-section mt-xxs">
          <div class="segment-datetime"></div>
          <div class="line-container">
            <div class="line line-border-aqua line-margin-left stopover-top-height"></div>
          </div>
          <div class="row-with-icon"></div>
        </div>
      }

      <div class="segment-section">
        <div class="segment-datetime">
          <p class="segment-time-label">{{ segment.arrivalDate | date : 'HH:mm' : 'UTC' }}</p>
          <p>{{ segment.arrivalDate | date : 'd MMM' : 'UTC' }}</p>
        </div>
        <div class="line-container" *ngIf="!lastSegment">
          <div class="line line-border-warning dot-margin-small"></div>
        </div>
        <div class="segment-info">
          <div class="aqua-dot mt-xxs"></div>
          <div class="text-wrapper-col">
            <p class="airport">{{ segment.arrivalAirportName }} ({{ segment.arrivalAirportCode }})</p>
            <p>{{ segment.arrivalCity | titlecase }}, {{ segment.arrivalCountry | titlecase }}</p>
          </div>
        </div>
      </div>
      <div class="segment-section" *ngIf="segment.showAirportChange">
        <div class="segment-datetime"></div>
        <div class="line-container"></div>
        <div class="d-flex">
          <img class="icon-m icon-clock" [src]="'icon_car' | pmsImage : { color: 'e0760b' }" alt="airport" />
          <span>
          <p class="stopover-label">Stop with an airport change. Transfer to the interchange airport on your own.</p>
        </span>
        </div>
      </div>
      <div class="segment-section" *ngIf="!lastSegment && !!segments[segmentIndex + 1]?.additionalTransport">
        <div class="segment-datetime"></div>
        <div class="line-container"></div>
        <div class="d-flex">
          <img
            class="icon-m icon-clock"
            [src]="segments[segmentIndex + 1].additionalTransport.iconName || ' ' | pmsImage : { color: 'e0760b' }"
            alt="additional-transport"
          />
          <span>
          <p class="stopover-label">
            {{ segments[segmentIndex + 1].additionalTransport.stopoverLabel }}
          </p>
        </span>
        </div>
      </div>
      <div class="segment-section" *ngIf="!lastSegment">
        <div class="segment-datetime"></div>
        <div class="line-container">
          <div class="line line-border-warning line-margin-left line-small"></div>
        </div>
        <div class="row-with-icon">
          <img class="icon-m icon-clock" [src]="'icon_clock' | pmsImage : { color: 'e0760b' }" alt="clock" />
          <span>
          <p class="stopover-label">Time between flights: {{ segments[segmentIndex + 1].timeForTransferConverted }}</p>
        </span>
        </div>
      </div>
    </div>
  </div>
}
