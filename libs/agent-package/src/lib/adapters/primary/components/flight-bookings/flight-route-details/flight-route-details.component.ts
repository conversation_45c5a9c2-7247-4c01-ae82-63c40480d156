import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import { EcsIconFilenamePipe } from '@shared/ui';
import { FlightSegmentQuery } from '../../../../../application/ports/primary/query/flight-segment.query';

@Component({
  selector: 'lib-flight-route-details',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, EcsIconFilenamePipe, PmsImagePipeModule],
  templateUrl: 'flight-route-details.component.html',
  styleUrls: ['flight-route-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FlightRouteDetailsComponent {
  @Input({ required: true }) segments!: FlightSegmentQuery[];
}
