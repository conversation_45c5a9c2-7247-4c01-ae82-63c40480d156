:host {
  display: block;
}

.container {
  border-top: var(--border-default);
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0 var(--spacing-s);
  margin: var(--spacing-xs) 0 0 var(--spacing-xxl);
  padding-top: var(--spacing-xs);

  .route {
    font-size: var(--font-size-xxs);
    font-weight: var(--font-weight-bold);
    margin-top: var(--spacing-xs);

    &:first-of-type {
      margin-top: initial;
    }
  }

  .item {
    display: flex;
    margin-top: var(--spacing-xxs);
    gap: var(--spacing-xxs);

    &.tooltip {
      cursor: pointer;
      --z-index: 3;
    }

    .label {
      color: var(--text-2-color);
      font-size: var(--font-size-xxs);
      line-height: var(--line-height-xxs);
    }
  }

  .flex-break {
    width: 100%;
  }
}
