@if (flightBookings$ | async; as flightBookings) {
  @for (flightBooking of flightBookings.flightBookings; let i = $index; track i) {
    <div baseBooking #baseBooking="baseBooking" class="rounded-container" [id]="'flightBookingIndex=' + i">
      @if (!flightBooking.isVisible) {
        <div class="booking-visibility-information">
          <ecs-icon class="icon-xl mr-m status alert" name="eye_crossed" />
          <div class="info">
            <p class="title">Hidden booking</p>
            <p class="description">This booking is not visible for the end user</p>
          </div>
        </div>
      }
      <div class="section-title-container justify-content-sb">
        <div class="title-container" [class.edit-booking]="flightBooking.canEdit">
          <p class="title">Flights</p>
          <p class="subtitle mr-l">{{ flightBooking.productId }}</p>
          <ui-status [status]="flightBooking.bookingStatus" />
          @if (flightBooking.tags) {
            @for (tag of flightBooking.tags; track $index) {
              <ui-badge class="ml-l" [text]="tag" />
            }
          }
          @if (flightBooking.lastModificationDate) {
            <p class="last-modification">
              edit: {{ flightBooking.lastModificationDate | date: 'HH:mm:ss dd.MM.yyyy' : 'UTC' }}
            </p>
          }
        </div>
        <div class="d-flex align-items-center">
          <ecs-button kind="tertiary" (click)="flightBookingDetailsSidebar.openSidebar()">Show details</ecs-button>

          <ui-sidebar #flightBookingDetailsSidebar [sidebarTitle]="'Flights - ' + flightBooking.productId">
            <ng-container
              *ngTemplateOutlet="
                flightDetailsSidebarContent;
                context: { flights: flightBooking.flights, flightBooking: flightBooking }
              "
            ></ng-container>
          </ui-sidebar>
          @if (flightBooking.canEdit) {
            <ecs-button kind="tertiary ml-xl" icon="pencil" iconPosition="right" (click)="editOptionsDialog.open()">
              Edit
            </ecs-button>
            <ui-backdrop-button class="ml-xs" size="medium">
              <a
                data-selector="edit-issue-redirect-anchor"
                class="option"
                (click)="
                  baseBooking.openEditPage(flightBooking.canIssue, flightBooking.linkQuery.getEditRouterLink('issue'))
                "
                [class.disabled]="!flightBooking.canIssue"
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !flightBooking.canIssue }"
                  name="check_line"
                ></ecs-icon>
                Issuing
              </a>

              <a
                data-selector="edit-booking-cancellation-anchor"
                class="option"
                [class.disabled]="!flightBooking.canCancel"
                (click)="
                  baseBooking.openEditPage(
                    flightBooking.canCancel,
                    flightBooking.linkQuery.getEditRouterLink('cancellation')
                  )
                "
                #link
                target="_blank"
              >
                <ecs-icon
                  class="option-icon"
                  [ngClass]="{ disabled: !flightBooking.canCancel }"
                  name="action_trashcan"
                ></ecs-icon>
                Cancel
              </a>

              <a
                data-selector="edit-booking-hide-anchor"
                class="option"
                [class.disabled]="!flightBooking.canChangeVisibility"
                (click)="
                  baseBooking.onChangeBookingVisibility(
                    flightBooking.canChangeVisibility,
                    flightBooking.isVisible,
                    flightBooking.staticProductId,
                    flightBooking.itemId
                  )
                "
                #link
                target="_blank"
              >
                @if (flightBooking.isVisible) {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !flightBooking.canChangeVisibility }"
                    name="eye_line_crossed"
                  ></ecs-icon>
                  Hide
                } @else {
                  <ecs-icon
                    class="option-icon"
                    [ngClass]="{ disabled: !flightBooking.canChangeVisibility }"
                    name="eye_line"
                  ></ecs-icon>
                  Show
                }
              </a>
            </ui-backdrop-button>
          }
        </div>
      </div>
      <div class="booking">
        <div class="booking-main-section-container">
          <div class="data-container mb-xl">
            <ui-copy-with-tooltip [textToCopy]="flightBooking.providerBookingId">
              <div class="data">
                <p class="label">Provider ID</p>
                <p class="value">{{ flightBooking.providerBookingId }}</p>
              </div>
            </ui-copy-with-tooltip>
            <div class="data">
              <p class="label">Provider</p>
              <p class="value">{{ flightBooking.providerName }}</p>
            </div>
            <div class="data">
              <p class="label">Ticketing optimizer</p>
              <p class="value">{{ flightBooking.ticketingOptimizer }}</p>
            </div>
            <ui-copy-with-tooltip [textToCopy]="flightBooking.officeId">
              <div class="data">
                <p class="label">Office ID</p>
                <p class="value">{{ flightBooking.officeId }}</p>
              </div>
            </ui-copy-with-tooltip>
            <div
              #providerStatusAnchorInDetails
              data-selector="provider-status-anchor-in-details"
              [ngClass]="['data', !!flightBooking.providerAdditionalMessage ? 'cursor-pointer' : '']"
              (click)="
                !!flightBooking.providerAdditionalMessage &&
                  baseBooking.onProviderStatusPopoverOpen(providerStatusAnchorInDetails)
              "
            >
              <p class="label">Provider status</p>
              <p class="value">{{ flightBooking.providerStatus }}</p>
            </div>
          </div>
          <div class="booking-main-data-container">
            @for (flight of flightBooking.flights; track $index) {
              <div class="booking-main-data-card card-hover-shadow half-width cursor-pointer">
                <div class="content" [openUiSidebar]="flightDetailsSidebar">
                  <lib-flight [flight]="flight" [flightBooking]="flightBooking" class="mb-xl d-block" />
                  <div class="d-flex justify-content-center">
                    <ecs-button
                      class="mt-auto w-100"
                      size="medium"
                      kind="contrast"
                      (click)="onRouteDetailsButtonClicked(flightRouteDetailsDialog, $event)"
                      data-selector="flight-booking-show-route-details-button"
                    >
                      Show route details
                    </ecs-button>
                  </div>
                </div>

                @if (flight.onlineCheckin.showOnlineCheckin) {
                  <div class="footer" [openUiSidebar]="onlineCheckin" data-selector="flight-booking-oc-footer">
                    <div class="footer-content">
                      <div>
                        <p class="oc-card-footer-title">Online checkin</p>
                        <ui-status class="small" [status]="flight.onlineCheckinStatus" />
                      </div>
                      <ecs-icon name="arrow_right" />
                    </div>
                  </div>
                }
                <ui-sidebar #onlineCheckin [sidebarTitle]="'Online check-in'">
                  <lib-online-checkin [onlineCheckin]="flight.onlineCheckin" />
                </ui-sidebar>
              </div>

              <ecs-dialog
                #flightRouteDetailsDialog
                class="ecs-dialog-standard flight-route-details-dialog"
                [hasBackdrop]="true"
                data-selector="flight-route-details-dialog"
                (ecsBackdropClick)="flightRouteDetailsDialog.close()"
                (ecsEscapeKeydown)="flightRouteDetailsDialog.close()"
              >
                <ecs-dialog-header
                  class="ecs-header"
                  slot="dialog-header"
                  [headerTitle]="'Flight route details'"
                  (ecsCloseButtonClick)="flightRouteDetailsDialog.close()"
                ></ecs-dialog-header>
                <div slot="dialog-body">
                  <lib-flight-route-details [segments]="flight.segments" />
                </div>
              </ecs-dialog>

              <ui-sidebar #flightDetailsSidebar [sidebarTitle]="'Flight - ' + flightBooking.productId">
                <ng-container
                  *ngTemplateOutlet="
                    flightDetailsSidebarContent;
                    context: { flights: [flight], flightBooking: flightBooking }
                  "
                ></ng-container>
              </ui-sidebar>
            }
          </div>
        </div>
        <div class="booking-sub-section-container">
          @for (passenger of flightBooking.passengers; track $index) {
            <div class="booking-sub-section-card" [ngClass]="{ removed: passenger.isRemoved }">
              <lib-passenger [passenger]="passenger" [hasTooltip]="true" />
            </div>
          }
        </div>
        @if (flightBooking.bookingPrice) {
          <div class="buyouts">
            <lib-booking-price
              [bookingPrice]="flightBooking.bookingPrice!"
              [ngClass]="{ 'mb-m': flightBooking.buyouts }"
              totalPriceLabel="Flight Price"
            ></lib-booking-price>

            <ecs-link
              class="d-block mt-m"
              size="small"
              data-selector="flight-bookings-price-preview-anchor"
              target="_blank"
              [href]="flightBooking.linkQuery.getPreviewRouterLink('price-elements')"
            >
              Provider price preview
            </ecs-link>
            @if (flightBooking.buyouts.length) {
              <div class="mt-xs">
                @for (buyout of flightBooking.buyouts; track $index) {
                  <lib-buyout
                    class="mt-m card-hover-shadow"
                    [buyout]="buyout"
                    [index]="$index"
                    (click)="sidebar.openSidebar()"
                  ></lib-buyout>
                  <ui-sidebar sidebarTitle="Buyout details" #sidebar>
                    <ui-buyout-details [buyout]="buyout" />
                  </ui-sidebar>
                }
              </div>
            }
          </div>
        }
      </div>

      <lib-issuing-attempts [issuingAttempts]="flightBooking.issuingAttempts" />
      <ecs-dialog
        #editOptionsDialog
        class="ecs-dialog-standard"
        [hasBackdrop]="true"
        data-selector="options-dialog"
        (ecsBackdropClick)="editOptionsDialog.close()"
        (ecsEscapeKeydown)="editOptionsDialog.close()"
      >
        <ecs-dialog-header
          class="ecs-header"
          slot="dialog-header"
          [headerTitle]="'Edit flight - ' + flightBooking.productId"
          (ecsCloseButtonClick)="editOptionsDialog.close()"
        ></ecs-dialog-header>
        <div class="body" slot="dialog-body">
          <p class="text-2-color mb-xl">Choose which one of element you want to edit</p>
          <div class="d-flex mb-s">
            <div
              class="square-option mr-s"
              data-selector="edit-flight-booking-passengers-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('passengers')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="user_general" />
              <p class="name">Passengers</p>
            </div>
            <div
              class="square-option mr-s"
              data-selector="edit-flight-booking-general-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('general')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="settings_lowercase" />
              <p class="name">General</p>
            </div>
            <div
              class="square-option mr-s"
              data-selector="edit-flight-booking-itinerary-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('itinerary')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="globe_world" />
              <p class="name">Itinerary</p>
            </div>
            <div
              class="square-option"
              data-selector="edit-flight-booking-baggage-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('baggage')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="registered_baggage" />
              <p class="name">Baggage</p>
            </div>
          </div>
          <div class="d-flex">
            <div
              class="square-option mr-s"
              data-selector="edit-flight-booking-price-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('price-elements')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="money_coins" />
              <p class="name">Price</p>
            </div>
            @if (!flightBookings.showBookingPriceBuyoutAssignment) {
              <div
                class="square-option mr-s"
                data-selector="edit-flight-booking-buyouts-anchor"
                [routerLink]="flightBooking.linkQuery.getEditRouterLink('buyouts')"
                (click)="editOptionsDialog.close()"
              >
                <ecs-icon class="icon-xl mb-xs" name="buyouts" />
                <p class="name">Buyouts</p>
              </div>
            }
            <div
              class="square-option mr-s"
              data-selector="edit-flight-booking-tickets-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('tickets')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="tickets" />
              <p class="name">Tickets</p>
            </div>
            <div
              class="square-option"
              data-selector="edit-flight-booking-seats-anchor"
              [routerLink]="flightBooking.linkQuery.getEditRouterLink('seats')"
              (click)="editOptionsDialog.close()"
            >
              <ecs-icon class="icon-xl mb-xs" name="seat" />
              <p class="name">Seats</p>
            </div>
          </div>
        </div>
      </ecs-dialog>
      <div>
        <ecs-popover
          #providerStatusPopover
          data-selector="provider-status-popover"
          id="front-layer"
          [relativePosition]="'right-bottom'"
          [anchorElement]="baseBooking.providerStatusAnchor"
          positionId="provider-additional-message"
        >
          <ecs-popover-header
            data-selector="provider-status-popover-header"
            headerTitle="Provider message"
            slot="popover-header"
            (ecsCloseButtonClick)="baseBooking.onProviderStatusPopoverClose()"
          >
          </ecs-popover-header>
          <div class="popover-body" slot="popover-body" data-selector="provider-status-popover-body">
            {{ flightBooking.providerAdditionalMessage }}
          </div>
        </ecs-popover>
        <ecs-backdrop
          #providerStatusPopoverBackdrop
          class="popover-backdrop"
          (click)="baseBooking.onProviderStatusPopoverClose()"
        ></ecs-backdrop>
      </div>
    </div>
  }
}

<ng-template #flightDetailsSidebarContent let-flights="flights" let-flightBooking="flightBooking">
  <div baseBooking #baseBooking="baseBooking" class="sidebar-content-container">
    @for (flight of flights; track flight) {
      <lib-flight [flight]="flight" [flightBooking]="flightBooking" />
      <div class="flight-route-details-container" [ngClass]="{ show: flight.isDetailsOpen }">
        <lib-flight-route-details class="mt-xxl-2" [segments]="flight.segments" />
      </div>
      <ecs-button
        class="mt-xxl-4 mb-xxl-4 flight-route-details-toggle-btn"
        [text]="true"
        [icon]="flight.flightRouteDetailsToggleButton.icon"
        [iconPosition]="'right'"
        [kind]="'primary'"
        size="small"
        data-selector="flight-bookings-flight-details-toggle-btn"
        (click)="flight.toggleDetails()"
      >
        {{ flight.flightRouteDetailsToggleButton.text }}
      </ecs-button>
    }

    <lib-list-items [items]="flightBooking.detailsItems" />

    <p class="sidebar-booking-detail-title">Creation&Issue</p>
    <div class="multi-row-data-container">
      <div class="booking-data-row">
        <div class="data">
          <p class="label">Created at</p>
          <p class="value">{{ flightBooking.createdDate | date: 'dd.MM.yyyy - HH:mm' : 'UTC' }}</p>
        </div>
        <div class="data">
          <p class="label">Issuing at</p>
          <p class="value">
            {{ flightBooking.issuingDate ? (flightBooking.issuingDate | date: 'dd.MM.yyyy - HH:mm' : 'UTC') : '-' }}
          </p>
        </div>
      </div>
    </div>
    <div class="sidebar-booking-section-spacer"></div>
    <p class="sidebar-booking-detail-title">Passengers</p>
    <div class="mb-xxl-2">
      @for (passenger of flightBooking.passengers; track passenger) {
        <lib-passenger
          class="sidebar-booking-data-card"
          [ngClass]="{ removed: passenger.isRemoved }"
          [passenger]="passenger"
        ></lib-passenger>
      }
    </div>
  </div>
</ng-template>
