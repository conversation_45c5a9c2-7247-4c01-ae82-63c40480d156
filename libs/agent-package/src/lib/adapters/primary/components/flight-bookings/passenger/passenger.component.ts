import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PassengerQuery } from '../../../../../application/ports/primary/query/passenger.query';

@Component({
  selector: 'lib-passenger',
  templateUrl: 'passenger.component.html',
  styleUrls: ['passenger.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PassengerComponent {
  @Input() hasTooltip = false;
  @Input({ required: true }) passenger!: PassengerQuery;
}
