import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { EcsIconFilenamePipe, UiBadgeComponent, UiStatusComponent } from '@shared/ui';
import { OnlineCheckinQuery } from '../../../../../application/ports/primary/query/online-checkin.query';

@Component({
  selector: 'lib-online-checkin',
  standalone: true,
  imports: [CommonModule, CoreAngularModule, EcsIconFilenamePipe, UiStatusComponent, UiBadgeComponent],
  templateUrl: 'online-checkin.component.html',
  styleUrl: 'online-checkin.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OnlineCheckinComponent {
  @Input({ required: true }) onlineCheckin!: OnlineCheckinQuery;
}
