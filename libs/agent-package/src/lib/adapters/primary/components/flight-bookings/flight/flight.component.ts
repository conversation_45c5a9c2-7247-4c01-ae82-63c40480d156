import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import { UiBadgeComponent } from '@shared/ui';
import { FlightBookingQuery } from '../../../../../application/ports/primary/query/flight-booking.query';
import { FlightQuery } from '../../../../../application/ports/primary/query/flight.query';

@Component({
  selector: 'lib-flight',
  standalone: true,
  templateUrl: 'flight.component.html',
  styleUrls: ['flight.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, CoreAngularModule, PmsImagePipeModule, UiBadgeComponent],
})
export class FlightComponent {
  @Input({ required: true }) flightBooking!: FlightBookingQuery;
  @Input({ required: true }) flight!: FlightQuery;
}
