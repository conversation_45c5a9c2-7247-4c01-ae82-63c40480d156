@import 'libs/shared/ui/src/assets/styles/booking-layout';

.flight-data-row {
  display: flex;
  justify-content: space-between;

  .departure-date {
    font-weight: var(--font-weight-bold);
  }

  .airline-container {
    display: flex;
    align-items: center;

    .airline-name {
      color: var(--text-2-color);
      font-size: var(--font-size-xxs);
      margin-right: var(--spacing-xxs);
    }

    .airline-logo {
      width: 32px;
      height: 32px;
    }
  }

  .flight-time {
    color: var(--text-2-color);
    font-size: var(--font-size-xxs);
  }

  .flight-date {
    font-size: var(--font-size-xxs);
    color: var(--text-3-color);
  }

  .airport-code {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    line-height: 36px;
    width: 70px;
  }

  .flight-course-container {
    align-items: center;
    display: flex;
    flex-direction: column;
    width: 94px;

    .flight-course-icon {
      width: 100%;
    }

    .flight-course-label {
      color: var(--text-2-color);
      margin-top: -17px;
      font-size: var(--font-size-xxs);
    }
  }

  .flight-point-container {
    display: flex;
    max-width: 120px;

    .flight-point-icon {
      height: 10px;
      vertical-align: middle;
    }

    .flight-point {
      font-size: var(--font-size-xxs);
      color: var(--color-gray-60);
    }
  }
}

