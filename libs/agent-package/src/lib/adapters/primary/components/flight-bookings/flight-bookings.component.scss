@import 'libs/shared/ui/src/assets/styles/booking-layout';
@import 'libs/shared/ui/src/assets/styles/components';
@import 'libs/shared/ui/src/assets/styles/options';
@import 'libs/shared/ui/src/assets/styles/dialog';
@import 'libs/shared/ui/src/assets/styles/status';

.flight-route-details-toggle-btn {
  display: flex;
  align-self: center;
}

.flight-route-details-container {
  max-height: 0;
  overflow: hidden;
  transition: max-height 500ms ease-in-out;

  &.show {
    max-height: 1000px;
  }
}

.oc-card-footer-title {
  color: var(--text-2-color);
  font-size: var(--font-size-xxs);
  line-height: var(--line-height-xxs);
}

.removed {
  background-color: var(--color-gray-05);
}

.popover-body {
  width: 400px;
}

.buyouts {
  min-width: 312px
}

.flight-route-details-dialog {
  --width: 678px;
  --max-height: 600px;
}
