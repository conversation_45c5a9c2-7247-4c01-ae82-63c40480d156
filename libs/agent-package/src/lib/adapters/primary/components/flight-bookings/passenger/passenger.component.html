<lib-person [person]="passenger.person" />
@if (passenger.hasFlightServices) {
  <div class="container">
    @if (passenger.flightServices.entireTrip) {
      @for (baggage of passenger.flightServices.entireTrip.baggageItems; track baggage; let index = $index) {
        @if (hasTooltip) {
          <ecs-tooltip class="item tooltip" kind="dark" offset="12" [text]="baggage.tooltipLabel">
            <ecs-icon class="icon-xs mr-xxs" [name]="baggage.baggageIcon | ecsIconFilename" />
            <p class="label">{{ baggage.description }}</p>
          </ecs-tooltip>
        } @else {
          <div class="item">
            <ecs-icon class="icon-xs mr-xxs" [name]="baggage.baggageIcon | ecsIconFilename" />
            <div class="label">
              <p>{{ baggage.countLabel }} {{ baggage.label }}</p>
              <div class="flex-break"></div>
              <p>{{ baggage.description }}</p>
            </div>
          </div>
        }
      }

      @if (passenger.flightServices.entireTrip.seat) {
        <div class="item">
          <ecs-icon class="icon-xs mr-xxs" name="seat" />
          <p class="label">{{ passenger.flightServices.entireTrip.seat.label }}</p>
        </div>
      }

      @if (passenger.flightServices.entireTrip.priorityBoarding) {
        <div class="item">
          <ecs-icon class="icon-xs mr-xxs" name="priority_boarding" />
          <p class="label">{{ passenger.flightServices.entireTrip.priorityBoarding.label }}</p>
        </div>
      }
    } @else {
      @for (segment of passenger.flightServices.segments; track segment.route) {
        <p class="route flex-break">{{ segment.route }}</p>
        @for (baggage of segment.services.baggageItems; track baggage) {
          @if (hasTooltip) {
            <ecs-tooltip class="item tooltip" kind="dark" offset="12" [text]="baggage.tooltipLabel">
              <ecs-icon class="icon-xs mr-xxs" [name]="baggage.baggageIcon | ecsIconFilename" />
              <p class="label">{{ baggage.description }}</p>
            </ecs-tooltip>
          } @else {
            <div class="item">
              <ecs-icon class="icon-xs mr-xxs" [name]="baggage.baggageIcon | ecsIconFilename" />
              <div class="label">
                <p>{{ baggage.label }}</p>
                <div class="flex-break"></div>
                <p>{{ baggage.description }}</p>
              </div>
            </div>
          }
        }

        @if (segment.services.seat) {
          <div class="item">
            <ecs-icon class="icon-xs mr-xxs" name="seat" />
            <p class="label">{{ segment.services.seat.label }}</p>
          </div>
        }

        @if (segment.services.priorityBoarding) {
          <div class="item">
            <ecs-icon class="icon-xs mr-xxs" name="priority_boarding" />
            <p class="label">{{ segment.services.priorityBoarding.label }}</p>
          </div>
        }
      }
    }
  </div>
}
@if (passenger.hasFlightTickets) {
  <div class="container">
    @for (flightTicket of passenger.flightTickets; track $index) {
      <div class="item">
        <ecs-icon class="icon-xs mr-xxs" [name]="flightTicket.ticketIcon | ecsIconFilename" />
        <p class="label">{{ flightTicket.label }}</p>
      </div>
    }
  </div>
}
