import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CoreAngularModule } from '@ecs/angular';
import { PmsImagePipeModule } from '@shared/images';
import {
  BuyoutDetailsComponent,
  EcsIconFilenamePipe,
  OpenUiSidebarDirectiveModule,
  UiBackdropButtonComponent,
  UiBadgeComponent,
  UiCopyWithTooltipComponent,
  UiSidebarComponentModule,
  UiStatusComponent,
} from '@shared/ui';
import { BaseBookingDirective } from '../../directives/base-booking/base-booking.directive';
import { BookingPriceComponent } from '../booking-elements/booking-price/booking-price.component';
import { BuyoutComponent } from '../booking-elements/buyout/buyout.component';
import { PersonComponent } from '../booking-elements/person/person.component';
import { IssuingAttemptsComponent } from "../issuing-attempts/issuing-attempts.component";
import { ListItemsComponent } from '../list-items/list-items.component';
import { FlightBookingsComponent } from './flight-bookings.component';
import { FlightRouteDetailsComponent } from './flight-route-details/flight-route-details.component';
import { FlightComponent } from './flight/flight.component';
import { OnlineCheckinComponent } from './online-checkin/online-checkin.component';
import { PassengerComponent } from './passenger/passenger.component';

@NgModule({
  imports: [
    BookingPriceComponent,
    CommonModule,
    CoreAngularModule,
    FlightComponent,
    FlightRouteDetailsComponent,
    OnlineCheckinComponent,
    OpenUiSidebarDirectiveModule,
    PersonComponent,
    PmsImagePipeModule,
    RouterLink,
    UiStatusComponent,
    UiSidebarComponentModule,
    BuyoutComponent,
    BuyoutDetailsComponent,
    UiBackdropButtonComponent,
    UiCopyWithTooltipComponent,
    EcsIconFilenamePipe,
    BaseBookingDirective,
    UiBadgeComponent,
    ListItemsComponent,
    IssuingAttemptsComponent,
  ],
  declarations: [FlightBookingsComponent, PassengerComponent],
  providers: [],
  exports: [FlightBookingsComponent],
})
export class FlightBookingsComponentModule {}
