@import 'libs/shared/ui/src/assets/styles/booking-layout';
@import 'libs/shared/ui/src/assets/styles/status';

.container {
  padding-bottom: var(--spacing-xl);
}

.passenger {
  padding: var(--spacing-l) var(--spacing-s);
  margin: var(--spacing-m) 0;
  border: 1px solid var(--color-gray-05);
  border-radius: var(--border-radius-xxs);
}

.oc-section-title {
  margin-top: var(--spacing-xxl-2);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-m);
}

.passenger-info-title {
  margin-left: var(--spacing-xs);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-xs);
  font-weight: var(--font-weight-bold);
}

.trip {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xxl-2);
  padding-bottom: var(--spacing-xxl-2);
  border-bottom: 1px dashed var(--color-gray-05);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  line-height: 24px;
}

.status-description {
  margin: var(--spacing-xs) 0 var(--spacing-xxl-2);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-xs);
}

.timeline-item {
  display: flex;
  font-size: var(--font-size-s);
  line-height: var(--line-height-m);
  font-weight: var(--font-weight-bold);

  &.inactive {
    color: var(--color-gray-60);
  }
}

.border-left {
  display: block;
  margin: var(--spacing-xs) 0 var(--spacing-xs) 9px;
  height: 50px;
  border-left: 2px solid var(--color-gray-100);

  &.inactive {
    color: var(--color-gray-60);
    border-left: 2px solid var(--color-gray-60);
  }
}

.boarding-pass {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
  line-height: 1.5;
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-m);
  margin-bottom: var(--spacing-m);
  border-radius: var(--border-radius-xxs);
  color: var(--color-primary-50);
  border: 1px solid var(--color-primary-50);

  &:hover {
    background-color: var(--color-primary-01);
  }

  &:last-of-type {
    margin-bottom: var(--spacing-xxl-2);
  }

  .button-icon {
    color: var(--color-primary-50);
    font-size: var(--font-size-l);
    margin-right: 4px;
    vertical-align: middle;
  }

  .button-text {
    font-size: var(--font-size-base);
    vertical-align: middle;
  }
}

.removed {
  background-color: var(--color-gray-05);
}

.badge-deleted {
  --background-color: var(--color-gray-40);
}
