import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { EcsDialog } from '@ecs/angular';
import { Observable } from 'rxjs';
import { FlightBookingsQuery } from '../../../../application/ports/primary/query/flight-bookings.query';
import {
  GETS_FLIGHT_BOOKINGS_QUERY,
  GetsFlightBookingsQueryPort,
} from '../../../../application/ports/primary/query/gets-flight-bookings.query-port';

@Component({
  selector: 'lib-flight-bookings',
  templateUrl: 'flight-bookings.component.html',
  styleUrls: ['flight-bookings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FlightBookingsComponent {
  readonly flightBookings$: Observable<FlightBookingsQuery> = this.getFlightBookingsQueryPort.getFlightBookingsQuery();

  constructor(
    @Inject(GETS_FLIGHT_BOOKINGS_QUERY) private readonly getFlightBookingsQueryPort: GetsFlightBookingsQueryPort,
  ) {}

  async onRouteDetailsButtonClicked(flightRouteDetailsDialog: EcsDialog, event: Event): Promise<void> {
    await flightRouteDetailsDialog.open();
    event.stopPropagation();
    event.preventDefault();
  }
}
