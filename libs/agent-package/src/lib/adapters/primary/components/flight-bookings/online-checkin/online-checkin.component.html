<div class="container">
  <div class="trip">
    <p>{{ onlineCheckin.departureCity }}</p>
    <ecs-icon class="icon-m ml-xs mr-xs" name="arrow_next" />
    <p>{{ onlineCheckin.arrivalCity }}</p>
  </div>

  <ui-status [status]="onlineCheckin.status" />
  @if (onlineCheckin.status.description) {
    <p class="status-description status" [ngClass]="onlineCheckin.status.category"
       [innerHTML]="onlineCheckin.status.description"></p>
  }

  @for (item of onlineCheckin.timeline; track $index) {
    @if (!$first) {
      <div class="border-left" [class.inactive]="!item.isActive"></div>
    }
    <div class="timeline-item" [class.inactive]="!item.isActive">
      <ecs-icon
        class="icon-m mr-m status"
        [ngClass]="item.status.category"
        [name]="item.status.icon | ecsIconFilename"
      />
      <span class="label">{{ item.status.status }}</span>
    </div>
  }

  @if (onlineCheckin.hasOnlineCheckinData) {
    <div class="multi-row-data-container mb-xxl-2 mt-xxl-2">
      <div class="booking-data-row">
        <div class="data">
          <p class="label">Required</p>
          <p class="value w-b-word">{{ onlineCheckin.required }}</p>
        </div>
        <div class="data">
          <p class="label">Performer</p>
          <p class="value w-b-word">{{ onlineCheckin.performer }}</p>
        </div>
      </div>
      <div class="booking-data-row">
        <div class="data">
          <p class="label">Reason</p>
          <p class="value w-b-word">{{ onlineCheckin.reason }}</p>
        </div>
      </div>
    </div>
    <p class="oc-section-title">Passengers</p>

    @for (passengerOCQuery of onlineCheckin.passengersOC; track passengerIndex; let passengerIndex = $index) {
      <div class="passenger" [ngClass]="{'removed': passengerOCQuery.isRemoved}">
        <div class="d-flex align-items-center justify-content-sb">
          <div class="d-flex">
            <ecs-icon [name]="passengerOCQuery.person.personIcon | ecsIconFilename" />
            <p class="passenger-info-title">{{ passengerOCQuery.person.fullName }}</p>
          </div>
          @if (passengerOCQuery.isRemoved) {
            <ui-badge class="badge-deleted" text="Deleted" />
          }
        </div>
        @if (onlineCheckin.showAdditionalInformation) {
          @if (passengerOCQuery.showStatus) {
            <ui-status class="small mt-m" [status]="passengerOCQuery.onlineCheckinStatus" />
          } @else if (passengerOCQuery.informationItems.length) {
            <div class="multi-row-data-container mt-m">
              <div class="booking-data-row flex-wrap">
                @for (informationItemQuery of passengerOCQuery.informationItems; track informationIndex; let informationIndex = $index) {
                  <div class="data">
                    <p class="label">{{ informationItemQuery.label }}</p>
                    <p class="value">{{ informationItemQuery.value }}</p>
                  </div>

                  @if ($odd) {
                    <div class="w-100 divider"></div>
                  }
                }
              </div>
            </div>
          }
        }
      </div>
    }

    @if (onlineCheckin.boardingPasses.length) {
      <p class="oc-section-title mb-m">Check-in documents</p>
      @for (boardingPass of onlineCheckin.boardingPasses; track $index) {
        <a class="boarding-pass mb-m" [href]="boardingPass.url" target="_blank">
          {{ boardingPass.label }}
          <ecs-icon class="ml-xs" name="new_window" />
        </a>
      }
    }
  }
</div>
