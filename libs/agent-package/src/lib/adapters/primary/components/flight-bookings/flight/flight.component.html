<div class="flight-data-row booking-data-card-spacer">
  <p class="departure-date">{{ flight.departureDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>

  <div class="airline-container">
    <p class="airline-name">{{ flight.airlineCode }}</p>
    <img
      class="airline-logo"
      [src]="'al_logo_signet_' + flight.airlineCode | pmsImage : { format: 'png?s=32x32' }"
      alt="airline-logo"
    />
  </div>

  <ui-badge [iconSrc]="flight.directionIcon" [text]="flight.flightDirection" />
</div>
<div
  data-selector="flight-open-route-details-dialog"
>
  <div class="flight-data-row align-items-center mb-xxs">
    <div class="flight-date">
      <p>{{ flight.departureDate | date : 'HH:mm' : 'UTC' }}</p>
      <p>{{ flight.departureDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>
    </div>
    <p class="flight-time">{{ flight.flightTimeConverted }}</p>
    <div class="flight-date ta-r">
      <p>{{ flight.arrivalDate | date : 'HH:mm' : 'UTC' }}</p>
      <p>{{ flight.arrivalDate | date : 'dd.MM.yyyy' : 'UTC' }}</p>
    </div>
  </div>
  <div class="flight-data-row mb-xxs">
    <p class="airport-code">{{ flight.departureAirportCode }}</p>
    <div class="flight-course-container">
      <img class="flight-course-icon" [src]="flight.flightStopoverInfo.icon | pmsImage" alt="flight-course" />
      <p class="flight-course-label">{{ flight.flightStopoverInfo.label }}</p>
    </div>
    <p class="airport-code ta-r">{{ flight.arrivalAirportCode }}</p>
  </div>
  <div class="flight-data-row">
    <div class="flight-point-container">
      <p class="flight-point">
        <ecs-icon class="flight-point-icon" name="pin" />
        {{ flight.departurePoint }}
      </p>
    </div>
    <div class="flight-point-container">
      <p class="flight-point ta-r">
        <ecs-icon class="flight-point-icon" name="pin" />
        {{ flight.arrivalPoint }}
      </p>
    </div>
  </div>
</div>
